import { ref } from "vue";
import { useItemSelection } from "@/composables/useItemSelection";
import { useScrollContainer } from "@/composables/useScrollContainer";
import { ticketService } from "@/api/services/ticket-service";
import type { DateInfo, SeatInfo } from "@/api/types/ticket";

/**
 * 简化座位名称，将三个字的座位名称简化为两个字
 * @param name 原始座位名称
 * @returns 简化后的座位名称
 */
export function simplifyName(name: string): string {
  const nameMap: Record<string, string> = {
    商务座: "商务",
    特等座: "特等",
    一等座: "一等",
    二等座: "二等",
    高级软卧: "高软",
    高级动卧: "高动",
    软卧: "软卧",
    硬卧: "硬卧",
    软座: "软座",
    硬座: "硬座",
    无座: "无座",
    二等包座: "包座",
    优选一等座: "优选",
    动卧: "动卧",
    一等卧: "一卧",
    二等卧: "二卧",
  };

  return nameMap[name] || name;
}

// 类型定义
export interface DateRange {
  startDate: string;
  endDate: string;
}

/**
 * 日期数据接口
 */
export interface DateItem {
  date: string;
  day: string;
  weekday: string;
}

/**
 * 火车票数据接口
 */
export interface TrainItem {
  id: string;
  departureTime: string;
  departureStation: string;
  arrivalTime: string;
  arrivalStation: string;
  duration: string;
  price: string;
  trainNumber: string;
  tags: SeatInfo[];
}

// 生成默认日期数据（当API请求失败时使用）
// 从当天开始，到未来15天为止（符合国内火车票预售期）
const generateDefaultDateItems = (): DateItem[] => {
  const items: DateItem[] = [];
  const today = new Date();

  // 中文星期几
  const weekdayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

  // 生成从今天开始的15天日期
  for (let i = 0; i < 15; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD
    const day = date.getDate().toString();
    const weekday = weekdayNames[date.getDay()];

    items.push({
      date: dateStr,
      day,
      weekday,
    });
  }

  return items;
};

// 默认日期数据（当API请求失败时使用）
const defaultDateItems: DateItem[] = generateDefaultDateItems();

// 默认去程火车票数据（不再使用，保留注释以便理解代码历史）
// 这些数据已被移除，现在从API获取数据

// 默认返程火车票数据（不再使用，保留注释以便理解代码历史）
// 这些数据已被移除，现在从API获取数据

// 响应式数据
export const dateItems = ref<DateItem[]>([...defaultDateItems]);
export const outboundTrains = ref<TrainItem[]>([]); // 初始化为空数组，不再使用默认数据
export const returnTrains = ref<TrainItem[]>([]); // 初始化为空数组，不再使用默认数据

// 选中的车票
export const selectedOutboundTrain = ref<string>("");
export const selectedReturnTrain = ref<string>("");

// 加载状态
export const isLoading = ref(false);

/**
 * 从API获取车票数据
 * @param cityName 目的地城市名称
 * @param departureDate 出发日期
 * @param returnDate 返回日期
 * @param departureCity 出发城市名称，默认为"杭州"
 */
export async function fetchTicketData(
  cityName: string,
  departureDate: string,
  returnDate?: string,
  departureCity: string = "杭州"
) {
  console.log(
    `开始获取车票数据: 目的地=${cityName}, 出发城市=${departureCity}, 出发日期=${departureDate}, 返回日期=${
      returnDate || "无"
    }`
  );

  if (!cityName || !departureDate) {
    console.error("城市名称或出发日期为空，无法获取车票数据");
    return;
  }

  isLoading.value = true;

  // 清除之前的选择
  const clearPreviousSelections = () => {
    // 清除去程车票选择
    const outboundItems = document.querySelectorAll(
      ".train-list-left .train-item"
    );
    outboundItems.forEach((item) => {
      item.classList.remove("selected");
    });

    // 清除返程车票选择
    const returnItems = document.querySelectorAll(
      ".train-list:not(.train-list-left) .train-item"
    );
    returnItems.forEach((item) => {
      item.classList.remove("selected");
    });
  };

  // 清除之前的选择
  clearPreviousSelections();

  try {
    console.log("调用 ticketService.searchTickets API...");
    const requestData = {
      city_name: cityName,
      departure_date: departureDate,
      return_date: returnDate,
      departure_city: departureCity,
    };
    console.log("请求参数:", requestData);

    const response = await ticketService.searchTickets(requestData);
    console.log("API 响应:", response);

    // 生成日期数据（如果API没有返回availableDates）
    // 使用默认的日期生成逻辑
    if (response.availableDates && response.availableDates.length > 0) {
      // 兼容旧版API
      console.log(
        `使用API返回的日期数据: ${response.availableDates.length} 个日期`
      );
      dateItems.value = response.availableDates.map((dateInfo: DateInfo) => ({
        date: dateInfo.date,
        day: dateInfo.day,
        weekday: dateInfo.weekday,
      }));
    } else {
      // 使用默认日期数据
      console.log("API 没有返回日期数据，使用默认日期生成逻辑");
      dateItems.value = [...defaultDateItems];
    }

    // 更新车票数据 - 处理新的API响应格式
    // 检查是否有去程车票
    if (response.departureTickets && response.departureTickets.length > 0) {
      console.log(`更新去程车票: ${response.departureTickets.length} 张`);
      outboundTrains.value = response.departureTickets;

      // 默认选择第一个去程车票
      if (outboundTrains.value.length > 0) {
        setTimeout(() => {
          const firstTrainId = outboundTrains.value[0].id;
          const trainElement = document.querySelector(
            `.train-list-left .train-item[data-train-id="${firstTrainId}"]`
          );
          if (trainElement) {
            trainElement.classList.add("selected");
            selectedOutboundTrain.value = firstTrainId;
            console.log(`默认选择去程车票: ${firstTrainId}`);
          }
        }, 300);
      }
    } else if (response.tickets && response.tickets.length > 0) {
      // 兼容旧版API
      console.log(`使用旧版API返回的车票数据: ${response.tickets.length} 张`);
      outboundTrains.value = response.tickets;

      // 默认选择第一个去程车票
      if (outboundTrains.value.length > 0) {
        setTimeout(() => {
          const firstTrainId = outboundTrains.value[0].id;
          const trainElement = document.querySelector(
            `.train-list-left .train-item[data-train-id="${firstTrainId}"]`
          );
          if (trainElement) {
            trainElement.classList.add("selected");
            selectedOutboundTrain.value = firstTrainId;
            console.log(`默认选择去程车票: ${firstTrainId}`);
          }
        }, 300);
      }
    } else {
      console.warn("API 返回的去程车票为空");
      outboundTrains.value = []; // 不再使用默认数据，而是显示空数组

      // 默认选择第一个去程车票
      if (outboundTrains.value.length > 0) {
        setTimeout(() => {
          const firstTrainId = outboundTrains.value[0].id;
          const trainElement = document.querySelector(
            `.train-list-left .train-item[data-train-id="${firstTrainId}"]`
          );
          if (trainElement) {
            trainElement.classList.add("selected");
            selectedOutboundTrain.value = firstTrainId;
            console.log(`默认选择去程车票: ${firstTrainId}`);
          }
        }, 300);
      }
    }

    // 检查是否有返程车票
    if (response.returnTickets && response.returnTickets.length > 0) {
      console.log(`更新返程车票: ${response.returnTickets.length} 张`);
      returnTrains.value = response.returnTickets;

      // 默认选择第一个返程车票
      if (returnTrains.value.length > 0) {
        setTimeout(() => {
          const firstTrainId = returnTrains.value[0].id;
          const trainElement = document.querySelector(
            `.train-list:not(.train-list-left) .train-item[data-train-id="${firstTrainId}"]`
          );
          if (trainElement) {
            trainElement.classList.add("selected");
            selectedReturnTrain.value = firstTrainId;
            console.log(`默认选择返程车票: ${firstTrainId}`);
          }
        }, 300);
      }
    } else if (returnDate) {
      // 如果有返程日期但没有返程车票数据
      console.warn("API 返回的返程车票为空");
      returnTrains.value = []; // 不再使用默认数据，而是显示空数组

      // 默认选择第一个返程车票
      if (returnTrains.value.length > 0) {
        setTimeout(() => {
          const firstTrainId = returnTrains.value[0].id;
          const trainElement = document.querySelector(
            `.train-list:not(.train-list-left) .train-item[data-train-id="${firstTrainId}"]`
          );
          if (trainElement) {
            trainElement.classList.add("selected");
            selectedReturnTrain.value = firstTrainId;
            console.log(`默认选择返程车票: ${firstTrainId}`);
          }
        }, 300);
      }
    } else {
      // 如果没有返程日期，清空返程车票
      console.log("没有返程日期，清空返程车票");
      returnTrains.value = [];
    }

    console.log("车票数据获取成功");
    return response;
  } catch (error) {
    console.error("获取车票数据失败:", error);
    // 只使用默认日期数据，车票数据保持为空
    console.log("使用默认日期数据，车票数据为空");
    dateItems.value = [...defaultDateItems];
    outboundTrains.value = []; // 不再使用默认数据
    returnTrains.value = []; // 不再使用默认数据
  } finally {
    isLoading.value = false;
    console.log("车票数据加载完成");
  }
}

// 组合式函数：日期选择逻辑
export function useDateSelection(
  options: { cityName?: string; departureCity?: string } = {}
) {
  const { cityName = "上海", departureCity = "杭州" } = options;

  // 响应式状态
  const selectedDates = ref<string[]>([]);
  const dateContainerRef = ref<HTMLElement | null>(null);
  const goTitleRef = ref<HTMLElement | null>(null);
  const returnTitleRef = ref<HTMLElement | null>(null);
  const selectedCity = ref<string>(cityName);
  const selectedDepartureCity = ref<string>(departureCity);

  // 标记日期范围
  const markDateRange = (startDate: string, endDate: string) => {
    if (!dateContainerRef.value) return;

    const dateItems = dateContainerRef.value.querySelectorAll(".date-item");
    const start = new Date(startDate);
    const end = new Date(endDate);

    dateItems.forEach((item) => {
      const itemDate = new Date(item.getAttribute("data-date") || "");

      // 清除之前的范围标记
      item.classList.remove("in-range");

      // 如果日期在范围内但不是开始或结束日期
      if (itemDate > start && itemDate < end) {
        item.classList.add("in-range");
      }

      // 标记开始和结束日期
      if (itemDate.getTime() === start.getTime()) {
        item.classList.add("selected");
        item.classList.add("range-start");
      }

      if (itemDate.getTime() === end.getTime()) {
        item.classList.add("selected");
        item.classList.add("range-end");
      }
    });
  };

  // 更新火车票显示
  const updateTrainDisplay = (startDate: string, endDate: string) => {
    if (!goTitleRef.value || !returnTitleRef.value) return;

    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    const formatDate = (date: Date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    };

    goTitleRef.value.textContent = `去程 (${formatDate(startDateObj)})`;
    returnTitleRef.value.textContent = `返程 (${formatDate(endDateObj)})`;

    // 添加一个简单的动画效果
    const trainItems = document.querySelectorAll(".train-item");
    trainItems.forEach((item) => {
      (item as HTMLElement).style.opacity = "0.5";
      setTimeout(() => {
        (item as HTMLElement).style.opacity = "1";
      }, 300);
    });

    // 从API获取车票数据
    fetchTicketData(
      selectedCity.value,
      startDate,
      endDate,
      selectedDepartureCity.value
    );
  };

  // 清除所有日期选择
  const clearDateSelections = () => {
    if (!dateContainerRef.value) return;

    selectedDates.value = [];
    const dateItems = dateContainerRef.value.querySelectorAll(".date-item");
    dateItems.forEach((el) => {
      el.classList.remove("selected");
      el.classList.remove("range-start");
      el.classList.remove("range-end");
      el.classList.remove("in-range");
    });
  };

  // 处理日期点击事件
  const handleDateClick = (event: Event) => {
    const target = event.currentTarget as HTMLElement;
    const date = target.getAttribute("data-date") || "";

    // 如果已经选了两个日期，重置选择
    if (selectedDates.value.length === 2) {
      clearDateSelections();
    }

    // 选择当前日期
    target.classList.add("selected");
    selectedDates.value.push(date);

    // 标记范围开始和结束
    if (selectedDates.value.length === 1) {
      target.classList.add("range-start");
      target.classList.add("range-end"); // 同时标记为结束，表示单日往返

      // 单日期选择时，departure_date 和 return_date 是同一天
      console.log(`单日期选择: ${date}，设置为往返同一天`);
      fetchTicketData(
        selectedCity.value,
        date,
        date,
        selectedDepartureCity.value
      );

      // 返回选中的日期范围（往返同一天）
      return {
        startDate: date,
        endDate: date,
      };
    } else if (selectedDates.value.length === 2) {
      target.classList.add("range-end");

      // 对日期进行排序，确保开始日期在前
      selectedDates.value.sort();

      // 标记范围内的日期
      markDateRange(selectedDates.value[0], selectedDates.value[1]);

      // 更新火车票信息显示
      updateTrainDisplay(selectedDates.value[0], selectedDates.value[1]);

      // 返回选中的日期范围
      return {
        startDate: selectedDates.value[0],
        endDate: selectedDates.value[1],
      };
    }

    return null;
  };

  // 设置城市
  const setCity = (city: string) => {
    selectedCity.value = city;
    console.log(`设置目的地城市: ${city}`);

    // 如果已经选择了日期，则重新获取车票数据
    if (selectedDates.value.length > 0) {
      if (selectedDates.value.length === 1) {
        // 单日期时，departure_date 和 return_date 是同一天
        console.log(`单日期选择: ${selectedDates.value[0]}，设置为往返同一天`);
        fetchTicketData(
          city,
          selectedDates.value[0],
          selectedDates.value[0],
          selectedDepartureCity.value
        );
      } else if (selectedDates.value.length === 2) {
        console.log(
          `日期范围: ${selectedDates.value[0]} 至 ${selectedDates.value[1]}`
        );
        fetchTicketData(
          city,
          selectedDates.value[0],
          selectedDates.value[1],
          selectedDepartureCity.value
        );
      }
    } else {
      console.log("没有选择日期，不获取车票数据");
    }
  };

  // 设置出发城市
  const setDepartureCity = (city: string) => {
    selectedDepartureCity.value = city;
    console.log(`设置出发城市: ${city}`);

    // 如果已经选择了日期，则重新获取车票数据
    if (selectedDates.value.length > 0) {
      if (selectedDates.value.length === 1) {
        // 单日期时，departure_date 和 return_date 是同一天
        console.log(`单日期选择: ${selectedDates.value[0]}，设置为往返同一天`);
        fetchTicketData(
          selectedCity.value,
          selectedDates.value[0],
          selectedDates.value[0],
          city
        );
      } else if (selectedDates.value.length === 2) {
        console.log(
          `日期范围: ${selectedDates.value[0]} 至 ${selectedDates.value[1]}`
        );
        fetchTicketData(
          selectedCity.value,
          selectedDates.value[0],
          selectedDates.value[1],
          city
        );
      }
    } else {
      console.log("没有选择日期，不获取车票数据");
    }
  };

  return {
    selectedDates,
    dateContainerRef,
    goTitleRef,
    returnTitleRef,
    selectedCity,
    selectedDepartureCity,
    markDateRange,
    updateTrainDisplay,
    clearDateSelections,
    handleDateClick,
    setCity,
    setDepartureCity,
  };
}

// 组合式函数：火车票选择逻辑
export function useTrainSelection(
  options: {
    onSelect?: (trainId: string, isGoList: boolean) => void;
  } = {}
) {
  const { onSelect } = options;

  // 使用通用的选择逻辑
  const goTrainSelection = useItemSelection({
    onSelect: (selected) => {
      if (onSelect && typeof selected === "string") {
        onSelect(selected, true);
      }
    },
  });

  const returnTrainSelection = useItemSelection({
    onSelect: (selected) => {
      if (onSelect && typeof selected === "string") {
        onSelect(selected, false);
      }
    },
  });

  // 滚动容器管理
  const goTrainScrollManager = useScrollContainer({
    hideScrollbar: true,
  });

  const returnTrainScrollManager = useScrollContainer({
    hideScrollbar: true,
  });

  const goTrainListRef = goTrainScrollManager.containerRef;
  const returnTrainListRef = returnTrainScrollManager.containerRef;

  // 处理火车票点击事件
  const handleTrainClick = (event: Event, isGoList: boolean) => {
    const target = event.currentTarget as HTMLElement;
    const trainId = target.getAttribute("data-train-id") || "";

    // 清除之前的选择
    const listSelector = isGoList
      ? ".train-list-left .train-item"
      : ".train-list:not(.train-list-left) .train-item";
    const items = document.querySelectorAll(listSelector);
    items.forEach((item) => {
      item.classList.remove("selected");
    });

    if (isGoList) {
      goTrainSelection.selectItem(trainId);
      selectedOutboundTrain.value = trainId;
    } else {
      returnTrainSelection.selectItem(trainId);
      selectedReturnTrain.value = trainId;
    }

    // 添加当前项目的选中状态
    target.classList.add("selected");
  };

  // 创建一个新的对象，只包含我们需要的属性和方法
  return {
    goTrainListRef,
    returnTrainListRef,
    handleTrainClick,
    goTrainSelection,
    returnTrainSelection,
    // 从 goTrainScrollManager 中提取滚动方法
    scrollToTop: goTrainScrollManager.scrollToTop,
    scrollToBottom: goTrainScrollManager.scrollToBottom,
    scrollToElement: goTrainScrollManager.scrollToElement,
    setScrollingEnabled: goTrainScrollManager.setScrollingEnabled,
    // 使用 goTrainScrollManager 的滚动状态
    goScrollPosition: goTrainScrollManager.scrollPosition,
    goIsScrolling: goTrainScrollManager.isScrolling,
    // 使用 returnTrainScrollManager 的滚动状态
    returnScrollPosition: returnTrainScrollManager.scrollPosition,
    returnIsScrolling: returnTrainScrollManager.isScrolling,
  };
}

// 组合式函数：组件初始化逻辑
export function useTimeSelectionInit(
  dateSelectionUtils: ReturnType<typeof useDateSelection>,
  trainSelectionUtils: ReturnType<typeof useTrainSelection>
) {
  const {
    selectedDates,
    dateContainerRef,
    clearDateSelections,
    selectedCity,
    selectedDepartureCity,
  } = dateSelectionUtils;

  const { goTrainListRef, returnTrainListRef } = trainSelectionUtils;

  // 初始化函数
  const initializeComponent = (cityName?: string, departureCity?: string) => {
    // 如果提供了城市名称，则更新选中的城市
    if (cityName) {
      selectedCity.value = cityName;
    }

    // 如果提供了出发城市，则更新选中的出发城市
    if (departureCity) {
      selectedDepartureCity.value = departureCity;
    }

    // 清除之前的选择
    clearDateSelections();

    // 获取当前日期作为默认日期
    const today = new Date();
    const formattedToday = today.toISOString().split("T")[0]; // 格式为 YYYY-MM-DD

    console.log(
      `初始化组件，当前日期: ${formattedToday}, 选中城市: ${selectedCity.value}`
    );

    // 默认选中第一个日期
    if (dateContainerRef.value) {
      // 查找当前日期对应的日期项
      let todayItem = dateContainerRef.value.querySelector(
        `.date-item[data-date="${formattedToday}"]`
      );

      // 如果找不到当前日期，则使用第一个日期项
      if (!todayItem) {
        console.log("找不到当前日期的日期项，使用第一个日期项");
        todayItem = dateContainerRef.value.querySelector(".date-item");
      }

      if (todayItem) {
        console.log(`选中日期项: ${todayItem.getAttribute("data-date")}`);
        todayItem.classList.add("selected");
        todayItem.classList.add("range-start");
        todayItem.classList.add("range-end"); // 同时标记为结束，表示单日往返
        const dateStr = todayItem.getAttribute("data-date") || formattedToday;
        selectedDates.value.push(dateStr);

        // 只有在明确提供了城市名称和出发城市时才调用车票查询接口
        if (cityName && departureCity) {
          console.log(
            `获取初始车票数据: 目的地=${cityName}, 出发城市=${departureCity}, 出发日期=${dateStr}, 返回日期=${dateStr}`
          );
          fetchTicketData(cityName, dateStr, dateStr, departureCity);
        } else {
          console.log("未提供明确的城市名称或出发城市，不调用车票查询接口");
          // 只使用默认日期数据，车票数据保持为空
          dateItems.value = [...defaultDateItems];
          outboundTrains.value = []; // 不再使用默认数据
          returnTrains.value = []; // 不再使用默认数据
        }
      } else {
        console.warn("找不到任何日期项，无法初始化");
      }
    } else {
      console.warn("dateContainerRef.value 为空，无法初始化日期选择");
    }

    // 默认选中两列中的第一个车次
    if (goTrainListRef.value) {
      const firstGoTrainItem =
        goTrainListRef.value.querySelector(".train-item");
      if (firstGoTrainItem) {
        firstGoTrainItem.classList.add("selected");
        const trainId = firstGoTrainItem.getAttribute("data-train-id") || "";
        selectedOutboundTrain.value = trainId;
      }
    }

    if (returnTrainListRef.value) {
      const firstReturnTrainItem =
        returnTrainListRef.value.querySelector(".train-item");
      if (firstReturnTrainItem) {
        firstReturnTrainItem.classList.add("selected");
        const trainId =
          firstReturnTrainItem.getAttribute("data-train-id") || "";
        selectedReturnTrain.value = trainId;
      }
    }
  };

  return {
    initializeComponent,
  };
}
