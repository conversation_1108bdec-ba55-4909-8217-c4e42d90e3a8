from typing import List
from loguru import logger

from app.schemas.travel_guide import (
    Attraction,
    GuideItem,
    Transportation,
    TravelGuideRequest,
)


class TravelGuideService:
    """旅行指南相关操作的服务。"""

    @staticmethod
    async def generate_travel_guide(request: TravelGuideRequest) -> List[GuideItem]:
        """
        为城市生成旅行指南。

        在实际应用中，这将调用AI服务或数据库。
        目前，我们返回模拟数据。
        """
        logger.info(
            f"正在为城市生成旅行指南: {request.city_id}, 持续时间: {request.duration} 天"
        )

        # 用于演示的模拟数据
        if request.city_id == "shanghai":
            return TravelGuideService._get_shanghai_guide()
        else:
            # 其他城市的默认指南
            return TravelGuideService._get_default_guide(request.city_id)

    @staticmethod
    def _get_shanghai_guide() -> List[GuideItem]:
        """获取上海的模拟旅行指南。"""
        guide = [
            Transportation(
                id="transport-initial",
                type="transportation",
                time="07:00 - 08:00",
                icon="✈️",
                duration="1小时",
                detail="从北京首都机场飞往上海虹桥机场",
            ),
            Attraction(
                id="shanghai-hotel",
                type="attraction",
                time="09:00 - 10:30",
                name="上海迎宾馆",
                description="上海迎宾馆是上海地标性建筑，融合了中西建筑风格，可以欣赏到独特的建筑艺术。",
                ticketPrice="免费",
                recommendedStay="1.5小时",
                imageUrl="https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                position="left",
            ),
            Transportation(
                id="transport-1",
                type="transportation",
                time="10:30 - 10:45",
                icon="🚕",
                duration="15分钟",
                detail="打车从迎宾馆南门到外滩观光隧道入口",
            ),
            Attraction(
                id="the-bund",
                type="attraction",
                time="11:00 - 13:00",
                name="外滩",
                description="外滩是上海最著名的商业街区之一，汇集了各类商场、餐厅和历史建筑。",
                ticketPrice="免费",
                recommendedStay="2小时",
                imageUrl="https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                position="right",
            ),
            Transportation(
                id="transport-2",
                type="transportation",
                time="13:00 - 13:20",
                icon="🚇",
                duration="20分钟",
                detail="地铁2号线从南京东路站到人民广场站",
            ),
            Attraction(
                id="howard-garden",
                type="attraction",
                time="13:30 - 15:00",
                name="豪生花园",
                description="豪生花园是上海最大的花园之一，园内植物种类丰富，环境幽雅，是休闲游玩的好去处。",
                ticketPrice="¥50",
                recommendedStay="1.5小时",
                imageUrl="https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80",
                position="left",
            ),
            Transportation(
                id="transport-3",
                type="transportation",
                time="15:00 - 15:10",
                icon="🚶",
                duration="10分钟",
                detail="步行从豪生花园西门到田子坊北入口",
            ),
            Attraction(
                id="tianzifang",
                type="attraction",
                time="15:30 - 17:30",
                name="田子坊",
                description="田子坊是上海历史悠久的街区，保存了大量的石库门建筑，充满浓厚的文化氛围。",
                ticketPrice="免费",
                recommendedStay="2小时",
                imageUrl="https://images.unsplash.com/photo-1598511726623-d3e9f2db00e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                position="right",
            ),
            Transportation(
                id="transport-4",
                type="transportation",
                time="17:30 - 17:55",
                icon="🚕",
                duration="25分钟",
                detail="打车从田子坊南门到外滩观景平台",
            ),
            Attraction(
                id="bund-night",
                type="attraction",
                time="18:30 - 20:30",
                name="外滩夜景",
                description="晚上的外滩灯火辨然，汇聚了世界各地的建筑风格，是欣赏上海夜景的最佳地点。",
                ticketPrice="免费",
                recommendedStay="2小时",
                imageUrl="https://images.unsplash.com/photo-1536599424071-0b215a388ba7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                position="left",
            ),
        ]
        return guide

    @staticmethod
    def _get_default_guide(city_id: str) -> List[GuideItem]:
        """获取任何城市的简单默认指南。"""
        guide = [
            Transportation(
                id=f"{city_id}-transport-1",
                type="transportation",
                time="08:00 - 09:00",
                icon="✈️",
                duration="1小时",
                detail=f"从出发地飞往{city_id}",
            ),
            Attraction(
                id=f"{city_id}-attraction-1",
                type="attraction",
                time="10:00 - 12:00",
                name=f"{city_id}景点1",
                description=f"{city_id}的著名景点，值得一游。",
                ticketPrice="¥100",
                recommendedStay="2小时",
                imageUrl="https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                position="left",
            ),
            Transportation(
                id=f"{city_id}-transport-2",
                type="transportation",
                time="12:00 - 12:30",
                icon="🚕",
                duration="30分钟",
                detail=f"从{city_id}景点1到{city_id}景点2",
            ),
            Attraction(
                id=f"{city_id}-attraction-2",
                type="attraction",
                time="13:00 - 15:00",
                name=f"{city_id}景点2",
                description=f"{city_id}的另一个著名景点，值得一游。",
                ticketPrice="¥80",
                recommendedStay="2小时",
                imageUrl="https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                position="right",
            ),
        ]
        return guide
