/**
 * 旅游指南相关API服务
 */
import { api } from "../instance";
import type { GenerateGuideRequest, GuideItem } from "../types/travel-guide";
import { handleErrorWithNotification } from "../utils/errorNotification";

/**
 * 旅游指南API服务
 */
export const travelGuideService = {
  /**
   * 生成旅游指南
   * @param request - 生成旅游指南请求参数
   * @returns 旅游指南项目列表
   */
  async generateGuide(request: GenerateGuideRequest): Promise<GuideItem[]> {
    try {
      const response = await api.post<GuideItem[]>(
        "/travel-guides/generate-guide",
        request
      );
      return response.data;
    } catch (error) {
      // 使用统一的错误处理
      handleErrorWithNotification(error, {
        customMessage: "生成旅游指南失败，请稍后再试",
      });
      // 返回空数组，避免UI出错
      return [];
    }
  },
};
