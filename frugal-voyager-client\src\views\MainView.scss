.main-title {
  text-align: center;
  font-size: 36px;
  margin: 40px 0 30px;
  font-weight: bold;
  letter-spacing: 1px;
  transition: transform 0.8s ease-in-out;
  position: relative; /* 确保transform正常工作 */
  z-index: 1; /* 确保显示层级 */
  will-change: transform; /* 优化transform性能 */

  /* 默认使用实色，确保在所有浏览器中都可见 */
  color: #1a365d;
  background: transparent; /* 默认透明背景 */

  /* 只在Webkit浏览器（Chrome, Safari, Edge）中使用渐变文字效果 */
  @supports (-webkit-background-clip: text) and (not (-moz-appearance: none)) {
    background: linear-gradient(
      135deg,
      #2d3748 0%,
      /* 深蓝灰色 */ #4a5568 50%,
      /* 中等蓝灰色 */ #1a365d 100% /* 深蓝色 */
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
}

.origin-input-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  transition: transform 0.8s ease-in-out;
  position: relative;
  z-index: 100; /* 确保始终显示在inbox-container之上 */
  pointer-events: auto; /* 确保按钮可点击 */
}

.origin-input {
  display: flex;
  align-items: center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  backdrop-filter: blur(15px);
  border-radius: 30px;
  padding: 12px 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.5);
  width: 450px; /* 增加宽度以容纳按钮 */
  max-width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: visible; /* 改为visible，防止按钮被裁剪 */

  &::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(
      45deg,
      rgba(111, 121, 133, 0.1) 0%,
      rgba(144, 156, 170, 0.1) 25%,
      rgba(209, 194, 189, 0.1) 50%,
      rgba(240, 198, 184, 0.1) 75%,
      rgba(226, 159, 132, 0.1) 100%
    );
    z-index: -1;
    filter: blur(10px);
  }

  input {
    flex: 1;
    border: none;
    padding: 5px 10px;
    font-size: 24px;
    outline: none;
    background: transparent;
    color: #1a365d;
    font-weight: 500;
    letter-spacing: 0.5px;
    height: 36px;
    line-height: 36px;
  }

  button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 20px;
    color: #1a365d;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    min-width: 36px; /* 确保最小宽度 */
    min-height: 36px; /* 确保最小高度 */
    max-width: 36px; /* 确保最大宽度 */
    max-height: 36px; /* 确保最大高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 2px;
    box-sizing: border-box; /* 明确设置box-sizing */
    flex-shrink: 0; /* 防止按钮被压缩 */

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(26, 54, 93, 0.2);
    }

    &:first-child {
      background: linear-gradient(
        135deg,
        rgba(111, 121, 133, 0.2) 0%,
        rgba(144, 156, 170, 0.2) 100%
      );
    }

    &:last-child {
      background: linear-gradient(
        135deg,
        rgba(209, 194, 189, 0.3) 0%,
        rgba(226, 159, 132, 0.3) 100%
      );
      font-weight: bold;
    }
  }
}

.inbox-container {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.5);
  max-width: 800px;
  width: 90%; /* 响应式宽度 */
  margin: 0 auto 20px; /* 减少底部边距 */
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.6);
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
  display: none;
  position: relative;
  z-index: 50; /* 确保显示在origin-input-container之下 */

  /* 响应式高度适配 */
  max-height: calc(100vh - 200px); /* 确保不超出视口高度 */

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(111, 121, 133, 0.05) 0%,
      rgba(144, 156, 170, 0.05) 25%,
      rgba(209, 194, 189, 0.05) 50%,
      rgba(240, 198, 184, 0.05) 75%,
      rgba(226, 159, 132, 0.05) 100%
    );
    z-index: -1;
    border-radius: 20px;
  }

  /* 不同屏幕尺寸的适配 */
  @media (max-height: 800px) {
    max-height: calc(100vh - 150px);
    margin-bottom: 10px;
  }

  @media (max-height: 700px) {
    max-height: calc(100vh - 120px);
    margin-bottom: 5px;
  }

  @media (max-height: 600px) {
    max-height: calc(100vh - 100px);
    margin-bottom: 5px;
  }
}

.inbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  background: linear-gradient(
    to right,
    rgba(111, 121, 133, 0.1) 0%,
    rgba(144, 156, 170, 0.1) 50%,
    rgba(209, 194, 189, 0.1) 100%
  );
}

.steps-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step {
  display: flex;
  align-items: center;

  &.active {
    .step-number {
      background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
      box-shadow: 0 2px 8px rgba(26, 54, 93, 0.3);
    }

    .step-text {
      color: #1a365d;
      font-weight: 600;
    }
  }

  &.in-progress {
    .step-number {
      background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
      box-shadow: 0 2px 8px rgba(26, 54, 93, 0.3);
      position: relative;
      overflow: hidden;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.3) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        animation: shimmer 1.5s infinite;
      }
    }

    .step-text {
      color: #1a365d;
      font-weight: 600;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, #d1c2bd, #f0c6b8);
        animation: pulse 1.5s infinite;
      }
    }
  }

  &.clickable {
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);

      .step-number {
        box-shadow: 0 4px 10px rgba(26, 54, 93, 0.4);
      }
    }
  }

  &:not(.clickable) {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  margin-right: 8px;
  background: linear-gradient(135deg, #6f7985 0%, #909caa 50%, #1a365d 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.step-divider {
  height: 2px;
  width: 20px;
  background: linear-gradient(to right, #d1c2bd, #f0c6b8);
  margin: 0 4px;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.4;
  }
}

.buttons-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.refresh-btn {
  background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(26, 54, 93, 0.2);
  position: relative;
  overflow: hidden;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.1) 100%
    );
  }

  &:hover {
    background-color: rgba(68, 89, 120, 1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(26, 54, 93, 0.3);
  }

  &.refresh-data-btn {
    background: linear-gradient(135deg, #6f7985 0%, #909caa 100%);

    &:hover {
      background-color: rgba(144, 156, 170, 0.9);
    }
  }

  &.close-btn {
    background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);

    &:hover {
      background-color: rgba(68, 89, 120, 1);
    }
  }
}

.inbox-content {
  /* 响应式高度设置 */
  min-height: 400px;
  max-height: calc(100vh - 300px); /* 基于视口高度动态计算 */
  padding: 24px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(26, 54, 93, 0.5) rgba(255, 255, 255, 0.1);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(26, 54, 93, 0.5);
    border-radius: 4px;

    &:hover {
      background: rgba(26, 54, 93, 0.7);
    }
  }

  /* 不同屏幕高度的适配 */
  @media (max-height: 800px) {
    max-height: calc(100vh - 250px);
    min-height: 350px;
    padding: 20px;
  }

  @media (max-height: 700px) {
    max-height: calc(100vh - 220px);
    min-height: 300px;
    padding: 16px;
  }

  @media (max-height: 600px) {
    max-height: calc(100vh - 200px);
    min-height: 250px;
    padding: 12px;
  }
}

.content-page {
  display: none;
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;

  &.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
  }
}

.day-selector {
  width: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0;
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  height: 410px;
  position: relative;
}

/* 通用的选择器头部样式 */
.day-selector-header {
  width: 100%;
  padding: 15px 0 10px 0;
  background: rgba(255, 255, 255, 0.3);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center;
}

/* 通用的选择器内容样式 */
.day-selector-content {
  padding: 10px;
  overflow-y: scroll;
  max-height: 400px;
  width: 100%;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  align-items: center;

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* 通用的标题样式 */
.day-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 15px;
  text-align: center;
}

/* 通用的日期项目样式 */
.day-item {
  width: 60px;
  min-height: 60px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &.selected {
    background: linear-gradient(
      135deg,
      rgba(26, 54, 93, 0.9) 0%,
      rgba(26, 54, 93, 0.7) 100%
    );
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

    .day-number,
    .day-label {
      color: white;
    }
  }
}

/* 通用的日期数字样式 */
.day-number {
  font-size: 18px;
  font-weight: 700;
  color: #1a365d;
}

/* 通用的日期文本样式 */
.day-label {
  font-size: 12px;
  color: #4a5568;
  margin-top: 4px;
}

/* 加载状态组件样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(
    135deg,
    rgba(111, 121, 133, 0.15) 0%,
    rgba(144, 156, 170, 0.2) 25%,
    rgba(209, 194, 189, 0.25) 50%,
    rgba(240, 198, 184, 0.2) 75%,
    rgba(226, 159, 132, 0.15) 100%
  );
  backdrop-filter: blur(8px);
  z-index: 10;
  border-radius: 0 0 20px 20px;
  animation: fadeIn 0.3s ease-in-out;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.spinner-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid rgba(26, 54, 93, 0.1);
  border-top-color: #1a365d;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  box-shadow: 0 0 15px rgba(26, 54, 93, 0.2);
}

.spinner-circle-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border: 4px solid rgba(209, 194, 189, 0.1);
  border-top-color: #d1c2bd;
  border-radius: 50%;
  animation: spin 0.8s linear infinite reverse;
  box-shadow: 0 0 10px rgba(209, 194, 189, 0.2);
}

.loading-text {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-top: 15px;
  padding: 8px 20px;
  border-radius: 20px;
  background: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.8) 0%,
    rgba(74, 85, 104, 0.8) 100%
  );
  color: white;
  box-shadow: 0 4px 10px rgba(26, 54, 93, 0.2);
  letter-spacing: 0.5px;
  animation: pulse 1.5s infinite;
  position: relative;
  overflow: hidden;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 2s infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
