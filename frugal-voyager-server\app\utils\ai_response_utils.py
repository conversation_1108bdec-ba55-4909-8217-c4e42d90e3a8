"""
AI 响应处理工具函数。

提供处理 AI 生成的响应（特别是 JSON 格式）的通用函数。
"""

from typing import List, Dict, Any, Optional
from loguru import logger

from app.utils.string_utils import extract_between_markers, remove_extra_whitespace
from app.utils.json_utils import fix_json_format, ensure_complete_json, safe_json_loads


def extract_json_from_markdown(text: str) -> str:
    """
    从 Markdown 代码块中提取 JSON 字符串。

    Args:
        text: 包含 Markdown 代码块的文本

    Returns:
        提取出的 JSON 字符串
    """
    logger.debug("从 Markdown 中提取 JSON")
    result = text

    # 提取 JSON 部分
    if "```json" in result:
        extracted = extract_between_markers(result, "```json", "```")
        if extracted:
            result = extracted
            logger.debug("提取 ```json 后的结果")
    elif "```" in result:
        extracted = extract_between_markers(result, "```", "```")
        if extracted:
            result = extracted
            logger.debug("提取 ``` 后的结果")

    # 去除换行和多余空格
    result = result.replace("\n", " ").strip()
    result = remove_extra_whitespace(result)
    logger.debug("去除换行和多余空格")

    return result


def parse_ai_response(response: str) -> List[Dict[str, Any]]:
    """
    解析 AI 响应，提取并修复 JSON 数据。

    Args:
        response: AI 的原始响应文本

    Returns:
        解析后的数据列表，如果解析失败则返回空列表
    """
    try:
        logger.debug(f"开始解析 AI 响应，长度: {len(response)}")

        # 1. 从 Markdown 中提取 JSON
        json_str = extract_json_from_markdown(response)

        # 2. 修复 JSON 格式
        json_str = fix_json_format(json_str)

        # 3. 确保 JSON 完整
        json_str = ensure_complete_json(json_str)

        # 4. 解析 JSON
        data = safe_json_loads(json_str)
        if data is None:
            logger.error("JSON 解析失败")
            return []

        if isinstance(data, list):
            logger.debug(f"成功解析 JSON，包含 {len(data)} 个项目")
            return data
        elif isinstance(data, dict):
            logger.debug("成功解析 JSON，包含 1 个对象")
            return [data]
        else:
            logger.error(f"解析结果不是列表或字典: {type(data)}")
            return []

    except Exception as e:
        logger.error(f"解析 AI 响应时出错: {e}")
        logger.debug(f"尝试解析的响应: {response[:200]}...")
        return []


def format_search_results(search_results: List[Dict[str, Any]]) -> str:
    """
    格式化搜索结果为字符串。

    Args:
        search_results: 搜索结果列表

    Returns:
        格式化后的搜索结果字符串
    """
    return "\n\n".join(
        [
            f"标题: {result['title']}\n内容: {result['content']}\n来源: {result['url']}"
            for result in search_results
        ]
    )
