/**
 * 通知服务
 *
 * 提供全局通知功能，用于显示各种类型的通知
 */
import { reactive } from "vue";

// 通知类型
export type NotificationType = "info" | "success" | "warning" | "error";

// 通知位置
export type NotificationPosition = "top" | "center" | "bottom";

// 通知配置
export interface NotificationOptions {
  title?: string;
  message: string;
  type?: NotificationType;
  duration?: number;
  position?: NotificationPosition;
}

// 通知状态
export const notificationState = reactive({
  show: false,
  title: "",
  message: "",
  type: "info" as NotificationType,
  duration: 5000, // 延长显示时间为5秒
  position: "top" as NotificationPosition, // 默认位置改为顶部
});

/**
 * 显示通知
 * @param options 通知配置
 */
export function showNotification(options: NotificationOptions): void {
  // 如果已经显示通知，先关闭
  if (notificationState.show) {
    notificationState.show = false;

    // 延迟一下再显示新通知，避免动画冲突
    setTimeout(() => {
      updateNotificationState(options);
    }, 300);
  } else {
    updateNotificationState(options);
  }
}

/**
 * 更新通知状态
 * @param options 通知配置
 */
function updateNotificationState(options: NotificationOptions): void {
  notificationState.title = options.title || "";
  notificationState.message = options.message;
  notificationState.type = options.type || "info";
  notificationState.duration =
    options.duration !== undefined ? options.duration : 5000;
  notificationState.position = options.position || "top"; // 默认位置为顶部
  notificationState.show = true;
}

/**
 * 关闭通知
 */
export function closeNotification(): void {
  notificationState.show = false;
}

/**
 * 显示信息通知
 * @param message 消息内容
 * @param title 标题（可选）
 */
export function showInfo(message: string, title?: string): void {
  showNotification({
    title,
    message,
    type: "info",
  });
}

/**
 * 显示成功通知
 * @param message 消息内容
 * @param title 标题（可选）
 */
export function showSuccess(message: string, title?: string): void {
  showNotification({
    title,
    message,
    type: "success",
  });
}

/**
 * 显示警告通知
 * @param message 消息内容
 * @param title 标题（可选）
 */
export function showWarning(message: string, title?: string): void {
  showNotification({
    title,
    message,
    type: "warning",
  });
}

/**
 * 显示错误通知
 * @param message 消息内容
 * @param title 标题（可选）
 */
export function showError(message: string, title?: string): void {
  showNotification({
    title,
    message,
    type: "error",
  });
}
