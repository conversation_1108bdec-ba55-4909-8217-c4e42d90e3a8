# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# OS specific
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
coverage.xml
.tox/
nosetests.xml
.hypothesis/
.pytest_cache/
