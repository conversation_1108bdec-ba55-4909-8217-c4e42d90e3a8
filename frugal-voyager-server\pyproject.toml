[project]
name = "frugal-voyager-server"
version = "0.1.0"
description = "API for the Frugal Voyager travel recommendation application"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.115.12",
    "loguru>=0.7.3",
    "uvicorn[standard]>=0.34.2",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.24.0",
    "langchain>=0.3.25",
    "langchain-community>=0.0.1",
    "openai>=1.77.0",
    "pypinyin>=0.54.0",
    "langgraph>=0.4.3",
    "langchain-openai>=0.3.16",
    "langchain-mcp-adapters>=0.0.11",
    "mcp>=1.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ["py313"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
