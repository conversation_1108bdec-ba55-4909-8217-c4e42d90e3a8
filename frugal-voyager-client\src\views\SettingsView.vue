<script setup lang="ts">
import { ref, onMounted } from "vue";
import { showError, showSuccess } from "@/services/notification-service";

// OpenRouter API Key
const openRouterKey = ref("");
const openRouterCityModel = ref("qwen/qwen3-235b-a22b");
const openRouterTravelGuideModel = ref("google/gemini-2.5-flash-preview");
const openRouterDefaultModel = ref("google/gemini-2.5-flash-preview");

// 高级设置展开状态
const showAdvancedSettings = ref(false);

// 加载状态
const isLoading = ref(false);
const isSaving = ref(false);

// 从本地存储加载设置
const loadSettings = () => {
  isLoading.value = true;
  try {
    // 从localStorage加载设置
    const savedKey = localStorage.getItem("openrouter_api_key") || "";
    const savedCityModel = localStorage.getItem("openrouter_city_model") || "";
    const savedTravelGuideModel =
      localStorage.getItem("openrouter_travel_guide_model") || "";
    const savedDefaultModel =
      localStorage.getItem("openrouter_default_model") || "";

    openRouterKey.value = savedKey;
    openRouterCityModel.value = savedCityModel;
    openRouterTravelGuideModel.value = savedTravelGuideModel;
    openRouterDefaultModel.value = savedDefaultModel;
  } catch (error) {
    console.error("加载设置时出错:", error);
    showError("加载设置失败", "设置错误");
  } finally {
    isLoading.value = false;
  }
};

// 保存设置到本地存储
const saveSettings = () => {
  isSaving.value = true;
  try {
    // 保存到localStorage
    localStorage.setItem("openrouter_api_key", openRouterKey.value);
    localStorage.setItem("openrouter_city_model", openRouterCityModel.value);
    localStorage.setItem(
      "openrouter_travel_guide_model",
      openRouterTravelGuideModel.value
    );
    localStorage.setItem(
      "openrouter_default_model",
      openRouterDefaultModel.value
    );

    showSuccess("设置已保存", "保存成功");
  } catch (error) {
    console.error("保存设置时出错:", error);
    showError("保存设置失败", "设置错误");
  } finally {
    isSaving.value = false;
  }
};

// 重置设置
const resetSettings = () => {
  if (
    confirm("确定要重置所有设置吗？这将清除所有已保存的API密钥和模型设置。")
  ) {
    openRouterKey.value = "";
    openRouterCityModel.value = "";
    openRouterTravelGuideModel.value = "";
    openRouterDefaultModel.value = "";

    // 清除localStorage中的设置
    localStorage.removeItem("openrouter_api_key");
    localStorage.removeItem("openrouter_city_model");
    localStorage.removeItem("openrouter_travel_guide_model");
    localStorage.removeItem("openrouter_default_model");

    showSuccess("设置已重置", "重置成功");
  }
};

// 组件挂载时加载设置
onMounted(() => {
  loadSettings();
});
</script>

<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1 class="settings-title">设置</h1>
      <p class="settings-subtitle">配置应用程序的API密钥和模型设置</p>
    </div>

    <div class="settings-content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <div class="spinner-circle"></div>
          <div class="spinner-circle-inner"></div>
        </div>
        <div class="loading-text">加载设置中...</div>
      </div>

      <div v-else class="settings-form">
        <!-- OpenRouter API Key 设置 -->
        <div class="settings-section">
          <h2 class="section-title">OpenRouter API 设置</h2>

          <div class="form-group">
            <label for="openrouter-key">API Key</label>
            <div class="input-container">
              <input
                id="openrouter-key"
                type="password"
                v-model="openRouterKey"
                placeholder="输入您的 OpenRouter API Key"
              />
            </div>
            <p class="help-text">
              用于访问 OpenRouter API 的密钥。您可以在
              <a
                href="https://openrouter.ai/keys"
                target="_blank"
                rel="noopener noreferrer"
                >OpenRouter 网站</a
              >
              上获取您的 API Key。
            </p>
          </div>

          <!-- 高级设置切换按钮 -->
          <div
            class="advanced-settings-toggle"
            @click="showAdvancedSettings = !showAdvancedSettings"
          >
            <div class="toggle-button">
              <span>高级设置</span>
              <span class="toggle-icon">{{
                showAdvancedSettings ? "▼" : "▶"
              }}</span>
            </div>
          </div>

          <!-- 高级设置内容 - 模型设置 -->
          <div class="advanced-settings" v-if="showAdvancedSettings">
            <div class="form-group">
              <label for="default-model">默认模型</label>
              <div class="input-container">
                <input
                  id="default-model"
                  type="text"
                  v-model="openRouterDefaultModel"
                  placeholder="例如: google/gemini-2.5-flash-preview"
                />
              </div>
              <p class="help-text">用于大多数 API 调用的默认模型</p>
            </div>

            <div class="form-group">
              <label for="city-model">城市推荐模型</label>
              <div class="input-container">
                <input
                  id="city-model"
                  type="text"
                  v-model="openRouterCityModel"
                  placeholder="例如: google/gemini-2.5-flash-preview"
                />
              </div>
              <p class="help-text">用于城市推荐 API 的模型</p>
            </div>

            <div class="form-group">
              <label for="travel-guide-model">旅游指南模型</label>
              <div class="input-container">
                <input
                  id="travel-guide-model"
                  type="text"
                  v-model="openRouterTravelGuideModel"
                  placeholder="例如: google/gemini-2.5-flash-preview"
                />
              </div>
              <p class="help-text">用于旅游指南生成 API 的模型</p>
            </div>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="settings-actions">
          <button class="save-btn" @click="saveSettings" :disabled="isSaving">
            {{ isSaving ? "保存中..." : "保存设置" }}
          </button>

          <button class="reset-btn" @click="resetSettings" :disabled="isSaving">
            重置设置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.settings-container {
  max-width: 800px;
  margin: 40px auto;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.5);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  animation: slideInFromTop 0.8s ease-in-out;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(111, 121, 133, 0.05) 0%,
      rgba(144, 156, 170, 0.05) 25%,
      rgba(209, 194, 189, 0.05) 50%,
      rgba(240, 198, 184, 0.05) 75%,
      rgba(226, 159, 132, 0.05) 100%
    );
    z-index: -1;
    border-radius: 20px;
  }
}

.settings-header {
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  background: linear-gradient(
    to right,
    rgba(111, 121, 133, 0.1) 0%,
    rgba(144, 156, 170, 0.1) 50%,
    rgba(209, 194, 189, 0.1) 100%
  );
}

.settings-title {
  font-size: 28px;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #1a365d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
  letter-spacing: 1px;
}

.settings-subtitle {
  font-size: 16px;
  color: #4a5568;
  margin: 0;
}

.settings-content {
  padding: 24px;
  position: relative;
  min-height: 400px;
}

.settings-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 20px;
  color: #1a365d;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(26, 54, 93, 0.1);
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 8px;
  }

  .input-container {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.7) 0%,
      rgba(255, 255, 255, 0.5) 100%
    );
    backdrop-filter: blur(5px);
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05),
      inset 0 2px 4px rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.6);

    input {
      width: 100%;
      padding: 12px 16px;
      font-size: 16px;
      border: none;
      background: transparent;
      color: #1a365d;
      outline: none;

      &::placeholder {
        color: rgba(26, 54, 93, 0.4);
      }
    }
  }

  .help-text {
    font-size: 14px;
    color: #4a5568;
    margin-top: 8px;

    a {
      color: #1a365d;
      text-decoration: none;
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.settings-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;

  button {
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
    color: white;
    flex: 1;
    box-shadow: 0 4px 10px rgba(26, 54, 93, 0.2);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(26, 54, 93, 0.3);
    }
  }

  .reset-btn {
    background: linear-gradient(
      135deg,
      rgba(111, 121, 133, 0.2) 0%,
      rgba(144, 156, 170, 0.2) 100%
    );
    color: #1a365d;

    &:hover:not(:disabled) {
      background: linear-gradient(
        135deg,
        rgba(111, 121, 133, 0.3) 0%,
        rgba(144, 156, 170, 0.3) 100%
      );
    }
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(
    135deg,
    rgba(111, 121, 133, 0.15) 0%,
    rgba(144, 156, 170, 0.2) 25%,
    rgba(209, 194, 189, 0.25) 50%,
    rgba(240, 198, 184, 0.2) 75%,
    rgba(226, 159, 132, 0.15) 100%
  );
  backdrop-filter: blur(8px);
  z-index: 10;
  animation: fadeIn 0.3s ease-in-out;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.spinner-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid rgba(26, 54, 93, 0.1);
  border-top-color: #1a365d;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  box-shadow: 0 0 15px rgba(26, 54, 93, 0.2);
}

.spinner-circle-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border: 4px solid rgba(209, 194, 189, 0.1);
  border-top-color: #d1c2bd;
  border-radius: 50%;
  animation: spin 0.8s linear infinite reverse;
  box-shadow: 0 0 10px rgba(209, 194, 189, 0.2);
}

.loading-text {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-top: 15px;
  padding: 8px 20px;
  border-radius: 20px;
  background: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.8) 0%,
    rgba(74, 85, 104, 0.8) 100%
  );
  color: white;
  box-shadow: 0 4px 10px rgba(26, 54, 93, 0.2);
  letter-spacing: 0.5px;
  animation: pulse 1.5s infinite;
  position: relative;
  overflow: hidden;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 2s infinite;
  }
}

// 高级设置切换按钮样式
.advanced-settings-toggle {
  margin: 20px 0;
  cursor: pointer;

  .toggle-button {
    display: inline-flex;
    align-items: center;
    padding: 10px 16px;
    background: linear-gradient(
      135deg,
      rgba(111, 121, 133, 0.15) 0%,
      rgba(144, 156, 170, 0.15) 100%
    );
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 1px solid rgba(26, 54, 93, 0.1);

    &:hover {
      background: linear-gradient(
        135deg,
        rgba(111, 121, 133, 0.25) 0%,
        rgba(144, 156, 170, 0.25) 100%
      );
      transform: translateY(-2px);
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #1a365d;
    }

    .toggle-icon {
      margin-left: 10px;
      font-size: 12px;
      transition: transform 0.3s ease;
    }
  }
}

// 高级设置内容区域样式
.advanced-settings {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.3) 100%
  );
  border-radius: 12px;
  padding: 20px;
  margin-top: 10px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.3s ease-in-out;

  .form-group:last-child {
    margin-bottom: 0;
  }
}

// 动画
@keyframes slideInFromTop {
  0% {
    transform: translateY(-30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
