"""
OpenRouter中间件

用于从请求头中获取OpenRouter API Key和模型配置
支持多种配置来源：请求头 > 临时配置 > 环境变量
"""

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from loguru import logger
from app.config import settings
from app.services.config_service import ConfigService


class OpenRouterMiddleware(BaseHTTPMiddleware):
    """
    从请求头中获取OpenRouter API Key和模型配置的中间件
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        # 保存原始配置
        original_api_key = settings.OPENROUTER_API_KEY
        original_default_model = settings.OPENROUTER_MODEL
        original_city_model = settings.OPENROUTER_CITY_MODEL
        original_travel_guide_model = settings.OPENROUTER_TRAVEL_GUIDE_MODEL
        original_models = settings.OPENROUTER_MODELS.copy()

        try:
            # 配置优先级：请求头 > 临时配置 > 环境变量

            # 1. 首先尝试从请求头获取配置
            header_api_key = request.headers.get("X-OpenRouter-API-Key")
            header_default_model = request.headers.get("X-OpenRouter-Default-Model")
            header_city_model = request.headers.get("X-OpenRouter-City-Model")
            header_travel_guide_model = request.headers.get(
                "X-OpenRouter-Travel-Guide-Model"
            )

            # 2. 如果请求头没有配置，尝试使用临时配置
            temp_config, config_source = ConfigService.get_effective_openrouter_config()

            # 3. 应用配置（按优先级）
            if header_api_key:
                logger.debug("使用请求头中的OpenRouter API Key")
                settings.OPENROUTER_API_KEY = header_api_key
            elif temp_config and temp_config.api_key:
                logger.debug(f"使用{config_source}配置中的OpenRouter API Key")
                settings.OPENROUTER_API_KEY = temp_config.api_key

            # 应用模型配置
            if header_default_model:
                logger.debug(f"使用请求头中的默认模型: {header_default_model}")
                settings.OPENROUTER_MODEL = header_default_model
                settings.OPENROUTER_MODELS["default"] = header_default_model
            elif temp_config and temp_config.default_model:
                logger.debug(
                    f"使用{config_source}配置中的默认模型: {temp_config.default_model}"
                )
                settings.OPENROUTER_MODEL = temp_config.default_model
                settings.OPENROUTER_MODELS["default"] = temp_config.default_model

            if header_city_model:
                logger.debug(f"使用请求头中的城市推荐模型: {header_city_model}")
                settings.OPENROUTER_CITY_MODEL = header_city_model
                settings.OPENROUTER_MODELS["city"] = header_city_model
            elif temp_config and temp_config.city_model:
                logger.debug(
                    f"使用{config_source}配置中的城市推荐模型: {temp_config.city_model}"
                )
                settings.OPENROUTER_CITY_MODEL = temp_config.city_model
                settings.OPENROUTER_MODELS["city"] = temp_config.city_model

            if header_travel_guide_model:
                logger.debug(f"使用请求头中的旅游指南模型: {header_travel_guide_model}")
                settings.OPENROUTER_TRAVEL_GUIDE_MODEL = header_travel_guide_model
                settings.OPENROUTER_MODELS["travel_guide"] = header_travel_guide_model
            elif temp_config and temp_config.travel_guide_model:
                logger.debug(
                    f"使用{config_source}配置中的旅游指南模型: {temp_config.travel_guide_model}"
                )
                settings.OPENROUTER_TRAVEL_GUIDE_MODEL = temp_config.travel_guide_model
                settings.OPENROUTER_MODELS["travel_guide"] = (
                    temp_config.travel_guide_model
                )

            # 继续处理请求
            response = await call_next(request)
            return response

        finally:
            # 恢复原始配置
            settings.OPENROUTER_API_KEY = original_api_key
            settings.OPENROUTER_MODEL = original_default_model
            settings.OPENROUTER_CITY_MODEL = original_city_model
            settings.OPENROUTER_TRAVEL_GUIDE_MODEL = original_travel_guide_model
            settings.OPENROUTER_MODELS = original_models
