import { ref } from 'vue';

/**
 * Item interaction management
 * Handles item interactions like click, expand, etc.
 * 
 * @param options Configuration options
 * @returns Item interaction utilities
 */
export function useItemInteraction(options: {
  expandable?: boolean;
  onItemClick?: (item: HTMLElement, event: Event) => void;
  onItemExpand?: (item: HTMLElement, expanded: boolean) => void;
} = {}) {
  const { expandable = false, onItemClick, onItemExpand } = options;
  
  // Currently expanded item
  const expandedItem = ref<HTMLElement | null>(null);
  
  /**
   * Handle item click
   * @param event Click event
   */
  const handleItemClick = (event: Event) => {
    const item = event.currentTarget as HTMLElement;
    
    // Call onItemClick callback if provided
    if (onItemClick) {
      onItemClick(item, event);
    }
    
    // Handle expandable items
    if (expandable) {
      toggleItemExpand(item);
    }
  };
  
  /**
   * Toggle item expand state
   * @param item Item to toggle
   * @returns Whether the item is expanded after toggling
   */
  const toggleItemExpand = (item: HTMLElement) => {
    const isExpanded = item.classList.contains('expanded');
    
    // Toggle expanded state
    if (isExpanded) {
      item.classList.remove('expanded');
      expandedItem.value = null;
    } else {
      // Collapse previously expanded item
      if (expandedItem.value && expandedItem.value !== item) {
        expandedItem.value.classList.remove('expanded');
      }
      
      // Expand current item
      item.classList.add('expanded');
      expandedItem.value = item;
    }
    
    // Call onItemExpand callback if provided
    if (onItemExpand) {
      onItemExpand(item, !isExpanded);
    }
    
    return !isExpanded;
  };
  
  /**
   * Check if an item is expanded
   * @param item Item to check
   * @returns Whether the item is expanded
   */
  const isItemExpanded = (item: HTMLElement) => {
    return item.classList.contains('expanded');
  };
  
  /**
   * Expand an item
   * @param item Item to expand
   */
  const expandItem = (item: HTMLElement) => {
    if (!isItemExpanded(item)) {
      toggleItemExpand(item);
    }
  };
  
  /**
   * Collapse an item
   * @param item Item to collapse
   */
  const collapseItem = (item: HTMLElement) => {
    if (isItemExpanded(item)) {
      toggleItemExpand(item);
    }
  };
  
  /**
   * Collapse all items
   * @param container Container element containing items
   * @param itemSelector Selector for items
   */
  const collapseAllItems = (container: HTMLElement, itemSelector: string) => {
    if (!container) return;
    
    const items = container.querySelectorAll(itemSelector);
    items.forEach((item) => {
      (item as HTMLElement).classList.remove('expanded');
    });
    
    expandedItem.value = null;
  };
  
  return {
    expandedItem,
    handleItemClick,
    toggleItemExpand,
    isItemExpanded,
    expandItem,
    collapseItem,
    collapseAllItems
  };
}
