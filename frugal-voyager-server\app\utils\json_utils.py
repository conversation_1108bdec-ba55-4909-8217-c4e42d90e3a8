"""
JSON处理工具函数。

提供处理JSON数据的通用函数。
"""
import json
import re
from typing import Any, Dict, List, Optional, Union
from loguru import logger


def fix_json_format(json_str: str) -> str:
    """
    修复常见的JSON格式错误。
    
    Args:
        json_str: 原始JSON字符串
        
    Returns:
        修复后的JSON字符串
    """
    logger.debug("修复JSON格式")
    result = json_str
    
    # 修复末尾的逗号
    if result.endswith(",]"):
        result = result.replace(",]", "]")
        logger.debug("修复了末尾的 ,]")
    
    if result.endswith(",}"):
        result = result.replace(",}", "}")
        logger.debug("修复了末尾的 ,}")
        
    # 单引号替换为双引号
    if "'" in result:
        result = result.replace("'", '"')
        logger.debug("将单引号替换为双引号")
        
    # 修复缺少引号的属性名
    # 只修复没有引号的属性名，更精确的正则表达式
    result = re.sub(r"([{,])\s*([a-zA-Z0-9_]+)(?=\s*:)", r'\1"\2"', result)
    
    # 修复多余的双引号问题
    # 将 "name"":"value" 这种模式修复为 "name":"value"
    result = re.sub(r'"([a-zA-Z0-9_]+)"":\s*"', r'"\1":', result)
    
    # 修复可能的连续引号问题，如 ""value"" 变为 "value"
    result = re.sub(r'""([^"]+)""', r'"\1"', result)
    
    # 修复 "key"":" 模式
    result = re.sub(r'"([a-zA-Z0-9_]+)"":(?!")', r'"\1":', result)
    
    # 修复 :"value"" 模式
    result = re.sub(r':(?:\s*)"([^"]+)""(?=[,}])', r':"\1"', result)
    
    return result


def ensure_complete_json(json_str: str) -> str:
    """
    确保JSON字符串是完整的。
    
    Args:
        json_str: JSON字符串
        
    Returns:
        确保完整的JSON字符串
    """
    result = json_str
    
    # 确保JSON是完整的
    if result.startswith("[") and not result.endswith("]"):
        logger.warning(f"JSON数组可能不完整，末尾字符: {result[-20:]}")
        if "]" in result:
            result = result[: result.rindex("]") + 1]
            logger.debug(f"截断到最后一个 ] 字符: {result[-20:]}")
    
    if result.startswith("{") and not result.endswith("}"):
        logger.warning(f"JSON对象可能不完整，末尾字符: {result[-20:]}")
        if "}" in result:
            result = result[: result.rindex("}") + 1]
            logger.debug(f"截断到最后一个 }} 字符: {result[-20:]}")
            
    return result


def safe_json_loads(json_str: str) -> Optional[Union[Dict[str, Any], List[Any]]]:
    """
    安全地加载JSON字符串。
    
    Args:
        json_str: JSON字符串
        
    Returns:
        解析后的JSON对象，如果解析失败则返回None
    """
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}")
        return None


def format_json(obj: Union[Dict[str, Any], List[Any]], indent: int = 2) -> str:
    """
    格式化JSON对象为字符串。
    
    Args:
        obj: JSON对象
        indent: 缩进空格数
        
    Returns:
        格式化后的JSON字符串
    """
    return json.dumps(obj, ensure_ascii=False, indent=indent)
