/**
 * 车票相关类型定义
 */

/**
 * 日期信息
 */
export interface DateInfo {
  /** 日期，格式为YYYY-MM-DD */
  date: string;
  /** 日期中的天，如"15" */
  day: string;
  /** 星期几，如"周六" */
  weekday: string;
}

/**
 * 座位信息
 */
export interface SeatInfo {
  /** 座位名称 */
  name: string;
  /** 座位数量 */
  count: number;
  /** 座位价格 */
  price: number;
}

/**
 * 车票信息
 */
export interface Ticket {
  /** 车票ID */
  id: string;
  /** 出发时间 */
  departureTime: string;
  /** 出发站 */
  departureStation: string;
  /** 到达时间 */
  arrivalTime: string;
  /** 到达站 */
  arrivalStation: string;
  /** 行程时长 */
  duration: string;
  /** 价格 */
  price: string;
  /** 车次号 */
  trainNumber: string;
  /** 座位信息列表 */
  tags: SeatInfo[];
}

/**
 * 车票搜索请求参数
 */
export interface TicketSearchRequest {
  /** 目的地城市名称 */
  city_name: string;
  /** 出发日期，格式为YYYY-MM-DD */
  departure_date: string;
  /** 返回日期，格式为YYYY-MM-DD（可选） */
  return_date?: string;
  /** 出发城市名称（可选，默认为"杭州"） */
  departure_city?: string;
  /** 已废弃，请使用city_name */
  city_id?: string;
}

/**
 * 车票搜索响应
 */
export interface TicketSearchResponse {
  /** 去程车票列表 */
  departureTickets: Ticket[];
  /** 返程车票列表 */
  returnTickets: Ticket[];

  /** @deprecated 已废弃，请使用 departureTickets 和 returnTickets */
  availableDates?: DateInfo[];
  /** @deprecated 已废弃，请使用 departureTickets */
  tickets?: Ticket[];
}
