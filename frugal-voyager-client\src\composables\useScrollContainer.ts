import { ref, onMounted, onUnmounted } from "vue";

/**
 * 滚动容器管理
 * 用于管理滚动容器的行为和状态
 *
 * @param options 配置选项
 * @returns 滚动容器相关的状态和方法
 */
export function useScrollContainer(
  options: {
    hideScrollbar?: boolean;
    autoScrollToTop?: boolean;
    onScroll?: (event: Event) => void;
  } = {}
) {
  const { hideScrollbar = true, autoScrollToTop = true, onScroll } = options;

  // 容器引用
  const containerRef = ref<HTMLElement | null>(null);

  // 滚动状态
  const scrollPosition = ref(0);
  const isScrolling = ref(false);

  /**
   * 处理滚动事件
   * @param event 滚动事件
   */
  const handleScroll = (event: Event) => {
    if (!containerRef.value) return;

    // 阻止事件冒泡，防止滚动事件传播到其他容器
    // 但保留事件的默认行为
    event.stopPropagation();

    // 更新滚动位置
    scrollPosition.value = containerRef.value.scrollTop;

    // 设置滚动状态
    isScrolling.value = true;

    // 延迟清除滚动状态
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      isScrolling.value = false;
    }, 150);

    // 调用滚动回调
    if (onScroll) {
      onScroll(event);
    }

    // 强制更新滚动位置（确保响应式更新）
    setTimeout(() => {
      if (containerRef.value) {
        scrollPosition.value = containerRef.value.scrollTop;
      }
    }, 0);
  };

  // 滚动结束检测的超时
  let scrollTimeout: number;

  /**
   * 滚动到顶部
   * @param smooth 是否使用平滑滚动
   */
  const scrollToTop = (smooth = true) => {
    if (!containerRef.value) return;

    if (smooth) {
      containerRef.value.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      containerRef.value.scrollTop = 0;
    }
  };

  /**
   * 滚动到底部
   * @param smooth 是否使用平滑滚动
   */
  const scrollToBottom = (smooth = true) => {
    if (!containerRef.value) return;

    if (smooth) {
      containerRef.value.scrollTo({
        top: containerRef.value.scrollHeight,
        behavior: "smooth",
      });
    } else {
      containerRef.value.scrollTop = containerRef.value.scrollHeight;
    }
  };

  /**
   * 滚动到指定元素
   * @param element 要滚动到的元素
   * @param smooth 是否使用平滑滚动
   * @param offset 距离元素顶部的偏移量
   */
  const scrollToElement = (element: HTMLElement, smooth = true, offset = 0) => {
    if (!containerRef.value || !element) return;

    const elementTop = element.offsetTop;

    if (smooth) {
      containerRef.value.scrollTo({
        top: elementTop - offset,
        behavior: "smooth",
      });
    } else {
      containerRef.value.scrollTop = elementTop - offset;
    }
  };

  /**
   * 启用或禁用滚动
   * @param enabled 是否启用滚动
   */
  const setScrollingEnabled = (enabled: boolean) => {
    if (!containerRef.value) return;

    if (enabled) {
      containerRef.value.style.overflowY = "auto";
    } else {
      containerRef.value.style.overflowY = "hidden";
    }
  };

  // 设置和清理
  onMounted(() => {
    if (containerRef.value) {
      // 添加滚动事件监听器
      containerRef.value.addEventListener("scroll", handleScroll);

      // 如果需要隐藏滚动条
      if (hideScrollbar) {
        containerRef.value.classList.add("scrollable-hidden");
      }

      // 如果需要自动滚动到顶部
      if (autoScrollToTop) {
        scrollToTop(false);
      }
    }
  });

  onUnmounted(() => {
    // 移除滚动事件监听器
    if (containerRef.value) {
      containerRef.value.removeEventListener("scroll", handleScroll);
    }

    // 清除超时
    clearTimeout(scrollTimeout);
  });

  return {
    containerRef,
    scrollPosition,
    isScrolling,
    scrollToTop,
    scrollToBottom,
    scrollToElement,
    setScrollingEnabled,
  };
}
