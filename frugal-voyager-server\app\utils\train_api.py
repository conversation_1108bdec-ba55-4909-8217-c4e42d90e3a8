import requests
import json
import datetime
import time
import random
import re
from loguru import logger
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from pypinyin import pinyin, lazy_pinyin, Style


base_url = "https://kyfw.12306.cn"
public_name = "/otn"

# 座位类型映射
SEAT_TYPES = {
    "9": {"name": "商务座", "short": "swz"},
    "P": {"name": "特等座", "short": "tz"},
    "M": {"name": "一等座", "short": "ydz"},
    "D": {"name": "优选一等座", "short": "ydz"},
    "O": {"name": "二等座", "short": "edz"},
    "S": {"name": "二等包座", "short": "edz"},
    "6": {"name": "高级软卧", "short": "gr"},
    "A": {"name": "高级动卧", "short": "gr"},
    "4": {"name": "软卧", "short": "rw"},
    "I": {"name": "一等卧", "short": "rw"},
    "F": {"name": "动卧", "short": "rw"},
    "3": {"name": "硬卧", "short": "yw"},
    "J": {"name": "二等卧", "short": "yw"},
    "2": {"name": "软座", "short": "rz"},
    "1": {"name": "硬座", "short": "yz"},
    "W": {"name": "无座", "short": "wz"},
    "WZ": {"name": "无座", "short": "wz"},
    "H": {"name": "其他", "short": "qt"},
}

# 常用的User-Agent列表，模拟不同浏览器
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
]


def create_session():
    """
    创建一个带有重试机制的requests会话

    Returns:
        配置好的requests.Session对象
    """
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=5,  # 最多重试5次
        backoff_factor=0.5,  # 重试间隔时间因子
        status_forcelist=[429, 500, 502, 503, 504],  # 这些状态码会触发重试
        allowed_methods=["GET", "POST"],  # 允许重试的HTTP方法
    )

    # 配置适配器
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 随机选择一个User-Agent
    user_agent = random.choice(USER_AGENTS)

    # 设置通用请求头
    session.headers.update(
        {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Connection": "keep-alive",
        }
    )

    return session


def get_ticket_list(
    train_no: str,
    from_station_telecode: str,
    to_station_telecode: str,
    depart_date: str,
):
    """
    获取车次经停信息
    :param train_no: 列车号
    :param from_station_telecode: 出发站编号
    :param to_station_telecode: 到达站编号
    :param depart_date: 出发日期
    :return: 车次经停信息
    """
    # 查询车次信息
    left_ticket_list_url = "/czxx"

    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 创建带有重试机制的会话
            session = create_session()

            # 添加特定的请求头
            session.headers.update(
                {
                    "Host": "kyfw.12306.cn",
                    "Referer": f"{base_url}{public_name}{left_ticket_list_url}/init",
                }
            )

            # 获取初始化cookies (添加超时设置)
            try:
                session.get(
                    f"{base_url}{public_name}{left_ticket_list_url}/init",
                    timeout=(5, 10),  # 连接超时5秒，读取超时10秒
                )
            except requests.exceptions.RequestException as e:
                logger.warning(f"获取初始化cookies时出错: {e}，尝试继续请求")
                # 即使获取cookies失败，也尝试继续请求

            # 在重试之间添加随机延迟，避免被识别为爬虫
            if retry_count > 0:
                delay = random.uniform(1, 3)
                logger.info(f"第 {retry_count+1} 次重试，等待 {delay:.2f} 秒...")
                time.sleep(delay)

            # 构造查询参数
            params = {
                "train_no": train_no,
                "from_station_telecode": from_station_telecode,
                "to_station_telecode": to_station_telecode,
                "depart_date": depart_date,
            }

            # 发送请求 (添加超时设置)
            logger.info(f"正在查询车次 {train_no} 的经停信息")
            response = session.get(
                f"{base_url}{public_name}{left_ticket_list_url}/queryByTrainNo",
                params=params,
                timeout=(5, 15),  # 连接超时5秒，读取超时15秒
            )

            # 判断是否获取成功
            if response.status_code != 200 or response.text == "":
                logger.warning(f"请求失败，状态码: {response.status_code}")
                retry_count += 1
                continue

            # 解析响应数据
            response_data = response.json()

            # 检查响应数据结构
            if "data" not in response_data or "data" not in response_data["data"]:
                logger.warning(f"响应数据结构异常: {response_data}")
                retry_count += 1
                continue

            return response_data["data"]["data"].json()

        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

        except Exception as e:
            logger.error(f"获取车次经停信息时出现未知错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

    # 如果所有重试都失败，返回空列表
    return []


def get_ticket_price(
    train_no: str,
    from_station_no: str,
    to_station_no: str,
    seat_types: str,
    train_date: str,
):
    """
    获取车票价格
    :param train_no: 列车号
    :param from_station_no: 出发站编号
    :param to_station_no: 到达站编号
    :param seat_types: 座位类型
    :param train_date: 出发日期
    :return: 车票价格
    """
    # 查询车票信息
    left_ticket_url = "/leftTicket"

    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 创建带有重试机制的会话
            session = create_session()

            # 添加特定的请求头
            session.headers.update(
                {
                    "Host": "kyfw.12306.cn",
                    "Referer": f"{base_url}{public_name}{left_ticket_url}/init",
                }
            )

            # 获取初始化cookies (添加超时设置)
            try:
                session.get(
                    f"{base_url}{public_name}{left_ticket_url}/init",
                    timeout=(5, 10),  # 连接超时5秒，读取超时10秒
                )
            except requests.exceptions.RequestException as e:
                logger.warning(f"获取初始化cookies时出错: {e}，尝试继续请求")
                # 即使获取cookies失败，也尝试继续请求

            # 在重试之间添加随机延迟，避免被识别为爬虫
            if retry_count > 0:
                delay = random.uniform(1, 3)
                logger.info(f"第 {retry_count+1} 次重试，等待 {delay:.2f} 秒...")
                time.sleep(delay)

            # 构造查询参数
            params = {
                "train_no": train_no,
                "from_station_no": from_station_no,
                "to_station_no": to_station_no,
                "seat_types": seat_types,
                "train_date": train_date,
            }

            # 发送请求 (添加超时设置)
            logger.info(f"正在查询车次 {train_no} 的票价信息")
            response = session.get(
                f"{base_url}{public_name}{left_ticket_url}/queryTicketPrice",
                params=params,
                timeout=(5, 15),  # 连接超时5秒，读取超时15秒
            )

            # 判断是否获取成功
            if response.status_code != 200 or response.text == "":
                logger.warning(f"请求失败，状态码: {response.status_code}")
                retry_count += 1
                continue

            # 解析响应数据
            response_data = response.json()

            # 检查响应数据结构
            if "data" not in response_data:
                logger.warning(f"响应数据结构异常: {response_data}")
                retry_count += 1
                continue

            # 解析车票价格
            price_info = response_data["data"]
            return price_info.json()

        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return 0

        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return 0

        except Exception as e:
            logger.error(f"获取车票价格时出现未知错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return 0

    # 如果所有重试都失败，返回0
    return 0


def get_train_all_city_from_net():
    """
    获取所有城市信息
    :return: 所有城市信息
    """
    # 获取所有城市信息
    city_url = "/resources/js/framework"

    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 创建带有重试机制的会话
            session = create_session()

            # 添加特定的请求头
            session.headers.update(
                {
                    "Host": "kyfw.12306.cn",
                    "Referer": f"{base_url}{public_name}{city_url}/init",
                }
            )

            # 获取初始化cookies (添加超时设置)
            try:
                session.get(
                    f"{base_url}{public_name}{city_url}/init",
                    timeout=(5, 10),  # 连接超时5秒，读取超时10秒
                )
            except requests.exceptions.RequestException as e:
                logger.warning(f"获取初始化cookies时出错: {e}，尝试继续请求")
                # 即使获取cookies失败，也尝试继续请求

            # 在重试之间添加随机延迟，避免被识别为爬虫
            if retry_count > 0:
                delay = random.uniform(1, 3)
                logger.info(f"第 {retry_count+1} 次重试，等待 {delay:.2f} 秒...")
                time.sleep(delay)

            # 发送请求 (添加超时设置)
            logger.info("正在获取所有城市信息")
            response = session.get(
                f"{base_url}{public_name}{city_url}/favorite_name.js",
                timeout=(5, 15),  # 连接超时5秒，读取超时15秒
            )

            # 判断是否获取成功
            if response.status_code != 200 or response.text == "":
                logger.warning(f"请求失败，状态码: {response.status_code}")
                retry_count += 1
                continue

            # 根据逗号进行字符串分割
            try:
                js_str = response.text
                js_str = js_str.split("'")

                if len(js_str) < 2:
                    logger.warning(f"响应数据格式异常: {js_str}")
                    retry_count += 1
                    continue

                js_str = js_str[1]
                js_str = js_str.split("@")

                # 获取城市信息
                city_info = []
                for item in js_str:
                    if item == "":
                        continue
                    item = item.split("|")
                    if len(item) < 3:
                        continue
                    city_info.append(
                        {
                            "name": item[1],
                            "station_code": item[2],
                            "ping_yin": "".join(lazy_pinyin(item[1])),
                            "ping_yin_short": item[0],
                        }
                    )

                # 检查是否成功解析到城市信息
                if not city_info:
                    logger.warning("未能解析到任何城市信息")
                    retry_count += 1
                    continue

                logger.info(f"成功获取 {len(city_info)} 个城市信息")

                # 将城市信息写入文件, 编码格式为utf-8
                try:
                    with open(
                        "types/train_all_station.json", "w", encoding="utf-8"
                    ) as f:
                        json.dump(city_info, f, ensure_ascii=False)
                    logger.info("城市信息已保存到文件")
                except Exception as e:
                    logger.error(f"保存城市信息到文件时出错: {e}")
                    # 即使保存失败，也返回城市信息

                return city_info

            except Exception as e:
                logger.error(f"解析城市信息时出错: {e}")
                retry_count += 1
                continue

        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

        except Exception as e:
            logger.error(f"获取城市信息时出现未知错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

    # 如果所有重试都失败，返回空列表
    return []


def get_tickets(
    fromDate: str, fromStation: str, toStation: str, purposeCodes: str = "ADULT"
):
    """
    获取车票信息
    :param fromDate: 出发日期
    :param fromStation: 出发站
    :param toStation: 到达站
    :param purposeCodes: 乘客类型
    :return: 车票信息
    """
    # 查询车票信息
    left_ticket_url = "/leftTicket"

    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 创建带有重试机制的会话
            session = create_session()

            # 添加特定的请求头
            session.headers.update(
                {
                    "Host": "kyfw.12306.cn",
                    "Referer": f"{base_url}{public_name}{left_ticket_url}/init",
                }
            )

            # 获取初始化cookies (添加超时设置)
            try:
                session.get(
                    f"{base_url}{public_name}{left_ticket_url}/init",
                    timeout=(5, 10),  # 连接超时5秒，读取超时10秒
                )
            except requests.exceptions.RequestException as e:
                logger.warning(f"获取初始化cookies时出错: {e}，尝试继续请求")
                # 即使获取cookies失败，也尝试继续请求

            # 在重试之间添加随机延迟，避免被识别为爬虫
            if retry_count > 0:
                delay = random.uniform(1, 3)
                logger.info(f"第 {retry_count+1} 次重试，等待 {delay:.2f} 秒...")
                time.sleep(delay)

            # 构造查询参数
            params = {
                "leftTicketDTO.train_date": fromDate,
                "leftTicketDTO.from_station": fromStation,
                "leftTicketDTO.to_station": toStation,
                "purpose_codes": purposeCodes,
            }

            # 发送请求 (添加超时设置)
            logger.info(
                f"正在查询从 {fromStation} 到 {toStation} 的车票，日期: {fromDate}"
            )
            response = session.get(
                f"{base_url}{public_name}{left_ticket_url}/query",
                params=params,
                timeout=(5, 15),  # 连接超时5秒，读取超时15秒
            )

            # 判断是否获取成功
            if response.status_code != 200 or response.text == "":
                logger.warning(f"请求失败，状态码: {response.status_code}")
                retry_count += 1
                continue

            # 解析响应数据
            response_data = response.json()

            # 检查响应数据结构
            if "data" not in response_data or "result" not in response_data["data"]:
                logger.warning(f"响应数据结构异常: {response_data}")
                retry_count += 1
                continue

            # 解析车次信息
            train_list = response_data["data"]["result"]
            train_info = []
            for train_item in train_list:
                try:
                    train_item_info = analysisOfTrainNumberInfor(train_item)
                    train_info.append(train_item_info)
                except Exception as e:
                    logger.error(f"解析车次信息时出错: {e}")
                    continue

            logger.info(f"成功获取 {len(train_info)} 个车次信息")
            return train_info

        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

        except Exception as e:
            logger.error(f"获取车票信息时出现未知错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

    # 如果所有重试都失败，返回空列表
    return []


# 解析车次信息
def analysisOfTrainNumberInfor(info: str):
    """
    解析车次信息
    :param info: 车次信息
    :return
    """

    data_list = info.split("|")

    data_info = {
        "secret_str": data_list[0],  # 无用
        "button_text_info": data_list[1],  # 按钮名字：预定
        "train_no": data_list[2],  # 列车号
        "train_code": data_list[3],  # 车次
        "start_station_code": data_list[4],  # 起始站编号
        "end_station_code": data_list[5],  # 终点站编号
        "from_station_code": data_list[6],  # 出发站编号
        "to_station_code": data_list[7],  # 到达站编号
        "start_time": data_list[8],  # 出发时间
        "arrive_time": data_list[9],  # 到达时间
        "total_time": data_list[10],  # 总耗时
        "can_web_buy": data_list[
            11
        ],  # 是否可购买：Y 可购买 N 不可购买 IS_TIME_NOT_BUY 时间未定
        "yp_info": data_list[12],  # 无用
        "start_train_date": data_list[13],  # 起始站出发日期
        "train_seat_feature": data_list[14],  # 无用
        "location_code": data_list[15],  # 无用
        "from_station_no": data_list[
            16
        ],  # 出发站站序（对应火车经停信息中的站序）01表示始发站，大于1则表示过站
        "to_station_no": data_list[17],  # 到达站站序（对应火车经停信息中的站序）
        "is_support_card": data_list[18],  # 是否支持二代身份证进站：1 支持 0 不支持
        "controlled_train_flag": data_list[19],  # 无用
        "gg_num": data_list[20],  # ？
        "gr_num": data_list[21],  # 高级软卧
        "qt_num": data_list[22],  # 其他
        "rw_num": data_list[23],  # 软卧，一等卧
        "rz_num": data_list[24],  # 软座
        "tz_num": data_list[25],  # 特等座
        "wz_num": data_list[26],  # 无座
        "yb_num": data_list[27],  # ？
        "yw_num": data_list[28],  # 硬卧，二等卧
        "yz_num": data_list[29],  # 硬座
        "edz_num": data_list[30],  # 二等座
        "ydz_num": data_list[31],  # 一等座
        "swz_num": data_list[32],  # 商务座
        "srrb_num": data_list[33],  # 动卧
        "yp_ex": data_list[34],  # 查询车票价格时的 seat_types 字段
        "seat_types": data_list[35],  # 座位类型
        "exchange_train_flag": data_list[36],  # 无用
    }

    # 添加额外字段
    if len(data_list) > 37:
        data_info["houbu_train_flag"] = (
            data_list[37] if len(data_list) > 37 else ""
        )  # 是否是后补车次
        data_info["houbu_seat_limit"] = (
            data_list[38] if len(data_list) > 38 else ""
        )  # 后补限制
        data_info["yp_info_new"] = (
            data_list[39] if len(data_list) > 39 else ""
        )  # 新的余票信息，包含价格
        data_info["dw_flag"] = data_list[46] if len(data_list) > 46 else ""  # 动卧标志
        data_info["seat_discount_info"] = (
            data_list[53] if len(data_list) > 53 else ""
        )  # 座位折扣信息

    return data_info


def extract_prices_from_ticket_data(ticket_data):
    """
    直接从车票数据中提取价格信息，无需额外API调用

    :param ticket_data: 车票数据，通过analysisOfTrainNumberInfor解析得到
    :return: 包含各座位类型价格的列表
    """
    # 常量定义
    PRICE_STR_LENGTH = 10
    DISCOUNT_STR_LENGTH = 5

    # 获取关键字段
    yp_ex = ticket_data.get("yp_ex", "")
    yp_info_new = ticket_data.get("yp_info_new", "")
    seat_discount_info = ticket_data.get("seat_discount_info", "")

    # 初始化结果
    prices = {}
    discounts = {}

    try:
        # 解析折扣信息
        for i in range(0, len(seat_discount_info) // DISCOUNT_STR_LENGTH):
            discount_str = seat_discount_info[
                i * DISCOUNT_STR_LENGTH : (i + 1) * DISCOUNT_STR_LENGTH
            ]
            if discount_str and len(discount_str) > 1:
                discounts[discount_str[0]] = int(discount_str[1:])

        # 解析座位类型和价格
        ex_list = re.split(r"[01]", yp_ex)
        ex_list = [ex for ex in ex_list if ex]  # 移除空字符串

        for index, ex in enumerate(ex_list):
            if ex in SEAT_TYPES and index * PRICE_STR_LENGTH < len(yp_info_new):
                seat_type = SEAT_TYPES[ex]
                price_str = yp_info_new[
                    index * PRICE_STR_LENGTH : (index + 1) * PRICE_STR_LENGTH
                ]

                if len(price_str) > 5:
                    try:
                        price = int(price_str[1:-5])
                        discount = discounts.get(ex, None)

                        short_type = seat_type["short"]
                        num_key = f"{short_type}_num"

                        prices[ex] = {
                            "seat_name": seat_type["name"],
                            "short": short_type,
                            "seat_type_code": ex,
                            "num": ticket_data.get(num_key, ""),
                            "price": price,
                            "discount": discount,
                        }
                    except (ValueError, IndexError) as e:
                        logger.error(f"解析价格出错: {e}, price_str={price_str}")
    except Exception as e:
        logger.error(f"提取价格信息失败: {e}")

    return list(prices.values())


def get_tickets_with_prices(
    fromDate: str, fromStation: str, toStation: str, purposeCodes: str = "ADULT"
):
    """
    获取车票信息，并直接解析价格信息

    :param fromDate: 出发日期
    :param fromStation: 出发站
    :param toStation: 到达站
    :param purposeCodes: 乘客类型
    :return: 包含价格信息的车票列表
    """
    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 获取基本车票信息
            tickets = get_tickets(fromDate, fromStation, toStation, purposeCodes)

            if not tickets:
                logger.warning("未获取到车票信息，无法提取价格")
                retry_count += 1
                if retry_count >= max_retries:
                    logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                    return []
                continue

            # 为每个车票添加价格信息
            for ticket in tickets:
                try:
                    # 直接从车票数据中提取价格
                    prices = extract_prices_from_ticket_data(ticket)
                    ticket["prices"] = prices

                    # 添加价格摘要信息
                    price_summary = []
                    for price_info in prices:
                        seat_name = price_info["seat_name"]
                        price = price_info["price"]
                        num = price_info["num"]
                        num_display = f"{num}张" if num.isdigit() else num
                        price_summary.append(
                            f"{seat_name}: {num_display}剩余 {price}元"
                        )

                    ticket["price_summary"] = price_summary
                except Exception as e:
                    logger.error(f"提取价格信息失败: {e}")
                    ticket["prices"] = []
                    ticket["price_summary"] = []

            logger.info(f"成功获取 {len(tickets)} 个车次的价格信息")
            return tickets

        except Exception as e:
            logger.error(f"获取车票价格信息时出现未知错误: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"已达到最大重试次数 {max_retries}，放弃请求")
                return []

    # 如果所有重试都失败，返回空列表
    return []


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) >= 4:
        fromDate = sys.argv[1]
        fromStation = sys.argv[2]
        toStation = sys.argv[3]
        purposeCodes = sys.argv[4] if len(sys.argv) > 4 else "ADULT"
    else:
        fromDate = datetime.datetime.now().strftime("%Y-%m-%d")  # 默认今天
        fromStation = "HZH"  # 默认杭州
        toStation = "SHH"  # 默认上海
        purposeCodes = "ADULT"  # 默认成人票

    # 获取车次信息（包含价格）
    result = get_tickets_with_prices(fromDate, fromStation, toStation, purposeCodes)

    # 输出 JSON 格式的结果
    print(json.dumps(result, ensure_ascii=False))
