<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>穷游旅行规划</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
      }

      body {
        color: #333;
        min-height: 100vh;
      }

      .main {
        background: linear-gradient(
          to bottom right,
          #6f7985 0%,
          /* 浅蓝色，占比更多 */ #909caa 20%,
          /* 浅蓝色，占比更多 */ #d1c2bd 60%,
          /* 粉色 */ #f0c6b8 80%,
          /* 橙色 */ #e29f84 100%
        );
        /* max-width: 1200px; */
        /* margin: 0 auto; */
        padding: 10px 50px 20px 50px;
        position: relative;
        min-height: 600px;
        height: 100vh;
      }

      header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
      }

      .logo {
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
      }

      .logo:hover {
        transform: translateY(-2px);
      }

      .logo img {
        height: 28px;
        filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
      }

      .nav-links {
        display: flex;
        gap: 15px;
        align-items: center;
      }

      .nav-links a {
        text-decoration: none;
        color: #1a365d;
        font-size: 14px;
        font-weight: 500;
        padding: 6px 12px;
        transition: all 0.3s ease;
        position: relative;
      }

      .nav-links a:not(.login-btn):hover {
        color: #4a5568;
      }

      .nav-links a:not(.login-btn):hover::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(to right, #d1c2bd, #f0c6b8);
        border-radius: 1px;
      }

      .login-btn {
        background: rgba(255, 255, 255, 0.25);
        color: #1a365d;
        padding: 6px 16px;
        border-radius: 20px;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
      }

      .login-btn:hover {
        background: rgba(255, 255, 255, 0.35);
      }

      .main-title {
        text-align: center;
        font-size: 36px;
        margin: 40px 0 30px;
        background: linear-gradient(
          135deg,
          #2d3748 0%,
          /* 深蓝灰色 */ #4a5568 50%,
          /* 中等蓝灰色 */ #1a365d 100% /* 深蓝色 */
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: bold;
        letter-spacing: 1px;
        transition: transform 0.8s ease-in-out;
      }

      .origin-input-container {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        transition: transform 0.8s ease-in-out;
      }

      .origin-input {
        display: flex;
        align-items: center;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.7) 0%,
          rgba(255, 255, 255, 0.5) 100%
        );
        backdrop-filter: blur(15px);
        border-radius: 30px;
        padding: 12px 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15),
          inset 0 2px 4px rgba(255, 255, 255, 0.5);
        width: 415px;
        max-width: 100%;
        border: 1px solid rgba(255, 255, 255, 0.6);
        position: relative;
        overflow: hidden;
      }

      .origin-input::before {
        content: "";
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: linear-gradient(
          45deg,
          rgba(111, 121, 133, 0.1) 0%,
          rgba(144, 156, 170, 0.1) 25%,
          rgba(209, 194, 189, 0.1) 50%,
          rgba(240, 198, 184, 0.1) 75%,
          rgba(226, 159, 132, 0.1) 100%
        );
        z-index: -1;
        filter: blur(10px);
      }

      .origin-input input {
        flex: 1;
        border: none;
        padding: 5px 10px;
        font-size: 24px;
        outline: none;
        background: transparent;
        color: #1a365d;
        font-weight: 500;
        letter-spacing: 0.5px;
        height: 36px;
        line-height: 36px;
      }

      .origin-input button {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 20px;
        color: #1a365d;
        transition: all 0.3s ease;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 2px;
      }

      .origin-input button:first-child {
        background: linear-gradient(
          135deg,
          rgba(111, 121, 133, 0.2) 0%,
          rgba(144, 156, 170, 0.2) 100%
        );
      }

      .origin-input button:last-child {
        background: linear-gradient(
          135deg,
          rgba(209, 194, 189, 0.3) 0%,
          rgba(226, 159, 132, 0.3) 100%
        );
        font-weight: bold;
      }

      .origin-input button:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(26, 54, 93, 0.2);
      }

      .inbox-container {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.7) 0%,
          rgba(255, 255, 255, 0.5) 100%
        );
        backdrop-filter: blur(15px);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15),
          inset 0 2px 4px rgba(255, 255, 255, 0.5);
        max-width: 800px;
        margin: 0 auto 60px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.6);
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
        display: none;
        position: relative;
      }

      .inbox-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(111, 121, 133, 0.05) 0%,
          rgba(144, 156, 170, 0.05) 25%,
          rgba(209, 194, 189, 0.05) 50%,
          rgba(240, 198, 184, 0.05) 75%,
          rgba(226, 159, 132, 0.05) 100%
        );
        z-index: -1;
        border-radius: 20px;
      }

      .inbox-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        background: linear-gradient(
          to right,
          rgba(111, 121, 133, 0.1) 0%,
          rgba(144, 156, 170, 0.1) 50%,
          rgba(209, 194, 189, 0.1) 100%
        );
      }

      .steps-container {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .step {
        display: flex;
        align-items: center;
      }

      .step-number {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        margin-right: 8px;
        background: linear-gradient(
          135deg,
          #6f7985 0%,
          #909caa 50%,
          #1a365d 100%
        );
        color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .step-text {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .step-divider {
        height: 2px;
        width: 20px;
        background: linear-gradient(to right, #d1c2bd, #f0c6b8);
        margin: 0 4px;
      }

      .step.active .step-number {
        background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
        box-shadow: 0 2px 8px rgba(26, 54, 93, 0.3);
      }

      .step.active .step-text {
        color: #1a365d;
        font-weight: 600;
      }

      .step.in-progress .step-number {
        background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
        box-shadow: 0 2px 8px rgba(26, 54, 93, 0.3);
        position: relative;
        overflow: hidden;
      }

      .step.in-progress .step-number::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.3) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        animation: shimmer 1.5s infinite;
      }

      .step.in-progress .step-text {
        color: #1a365d;
        font-weight: 600;
        position: relative;
      }

      .step.in-progress .step-text::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, #d1c2bd, #f0c6b8);
        animation: pulse 1.5s infinite;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      @keyframes pulse {
        0% {
          opacity: 0.4;
        }
        50% {
          opacity: 1;
        }
        100% {
          opacity: 0.4;
        }
      }

      .refresh-btn {
        background: linear-gradient(135deg, #4a5568 0%, #1a365d 100%);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 10px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(26, 54, 93, 0.2);
        position: relative;
        overflow: hidden;
      }

      .refresh-btn::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 50%,
          rgba(0, 0, 0, 0.1) 100%
        );
      }

      .refresh-btn:hover {
        background-color: rgba(68, 89, 120, 1);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(26, 54, 93, 0.3);
      }

      .inbox-content {
        min-height: 460px;
        max-height: 460px;
        padding: 24px;
        background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 0.3) 0%,
          rgba(255, 255, 255, 0.1) 100%
        );
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(26, 54, 93, 0.5) rgba(255, 255, 255, 0.1);
        transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
      }

      .inbox-content::-webkit-scrollbar {
        width: 8px;
      }

      .inbox-content::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      .inbox-content::-webkit-scrollbar-thumb {
        background: rgba(26, 54, 93, 0.5);
        border-radius: 4px;
      }

      .inbox-content::-webkit-scrollbar-thumb:hover {
        background: rgba(26, 54, 93, 0.7);
      }

      .content-page {
        display: none;
        opacity: 0;
        transform: translateX(20px);
        transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
      }

      .content-page.active {
        display: block;
        opacity: 1;
        transform: translateX(0);
      }

      .item-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      /* 火车票选择样式 */
      .train-container {
        display: flex;
        height: 410px;
        position: relative;
        overflow: hidden;
      }

      .train-list-container {
        flex: 1;
        display: flex;
        max-width: calc(100% - 100px);
      }

      .train-list {
        flex: 1;
        overflow-y: scroll;
        padding: 0px 5px;
        max-height: 410px;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
      }

      .train-list::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      .train-list-left {
        border-right: 1px dashed rgba(26, 54, 93, 0.2);
      }

      /* 通用的日期选择器样式 */
      .train-date-selector,
      .day-selector {
        width: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0;
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        height: 410px;
        position: relative;
      }

      /* 通用的选择器头部样式 */
      .date-selector-header,
      .day-selector-header {
        width: 100%;
        padding: 15px 0 10px 0;
        background: rgba(255, 255, 255, 0.3);
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        position: sticky;
        top: 0;
        z-index: 2;
        text-align: center;
      }

      /* 通用的选择器内容样式 */
      .date-selector-content,
      .day-selector-content {
        padding: 10px;
        overflow-y: scroll;
        max-height: 400px;
        width: 100%;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .date-selector-content::-webkit-scrollbar,
      .day-selector-content::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      /* 通用的标题样式 */
      .date-title,
      .day-title {
        font-size: 14px;
        font-weight: 600;
        color: #1a365d;
        margin-bottom: 15px;
        text-align: center;
      }

      /* 通用的日期项目样式 */
      .date-item,
      .day-item {
        width: 60px;
        min-height: 60px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(255, 255, 255, 0.7) 100%
        );
        border-radius: 8px;
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
      }

      /* 通用的选中状态样式 */
      .date-item.selected,
      .day-item.selected {
        background: linear-gradient(
          135deg,
          rgba(26, 54, 93, 0.9) 0%,
          rgba(26, 54, 93, 0.7) 100%
        );
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      /* 通用的选中文本样式 */
      .date-item.selected .date-weekday,
      .date-item.selected .date-day,
      .day-item.selected .day-number,
      .day-item.selected .day-label {
        color: white;
      }

      /* 日期范围选择特有样式 */
      .date-item.range-start:after,
      .date-item.range-end:after {
        content: "";
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: #4fd1c5;
        border-radius: 3px;
      }

      .date-item.in-range {
        background: linear-gradient(
          135deg,
          rgba(79, 209, 197, 0.2) 0%,
          rgba(79, 209, 197, 0.1) 100%
        );
        transform: translateY(-1px);
      }

      /* 通用的日期数字样式 */
      .date-day,
      .day-number {
        font-size: 18px;
        font-weight: 700;
        color: #1a365d;
      }

      /* 通用的日期文本样式 */
      .date-weekday,
      .day-label {
        font-size: 12px;
        color: #4a5568;
        margin-top: 4px;
      }

      .train-item {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.8) 0%,
          rgba(255, 255, 255, 0.6) 100%
        );
        border-radius: 12px;
        padding: 12px;
        margin-bottom: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        border: 2px solid transparent;
        width: calc(100% - 10px);
      }

      .train-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }

      .train-item.selected {
        border-color: #4fd1c5;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(255, 255, 255, 0.7) 100%
        );
        box-shadow: 0 6px 20px rgba(79, 209, 197, 0.2);
      }

      .train-item.selected::after {
        content: "✓";
        position: absolute;
        top: -10px;
        right: -10px;
        width: 25px;
        height: 25px;
        background: #4fd1c5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      .train-item-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
      }

      .train-time {
        font-size: 20px;
        font-weight: 700;
        color: #1a365d;
      }

      .train-duration {
        font-size: 12px;
        color: #718096;
        text-align: center;
        margin-top: 4px;
      }

      .train-stations {
        font-size: 14px;
        color: #4a5568;
      }

      .train-info {
        display: flex;
        justify-content: space-between;
        margin-top: 12px;
        align-items: center;
      }

      .train-number {
        font-size: 13px;
        color: #718096;
      }

      .train-price {
        font-size: 18px;
        font-weight: 700;
        color: #ed8936;
      }

      .train-tags {
        display: flex;
        gap: 8px;
        margin-top: 8px;
      }

      .train-tag {
        font-size: 12px;
        color: #38b2ac;
        background: rgba(56, 178, 172, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
      }

      /* 计划安排模块样式 */
      .itinerary-container {
        display: flex;
        height: 410px;
        position: relative;
        overflow: hidden;
      }

      .timeline-container {
        flex: 1;
        display: flex;
        max-width: calc(100% - 100px);
        position: relative;
        overflow-y: scroll;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
      }

      .timeline-container::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      .timeline {
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 4px;
        background: rgba(68, 89, 120, 1);
        transform: translateX(-50%);
        z-index: 0;
      }

      /* 移除不需要的连接线 */
      .timeline-connector {
        display: none;
      }

      /* 确保时间点之间的连接 */
      .timeline-item,
      .transportation-item {
        position: relative;
      }

      /* 确保每个时间线项目和交通方式项目之间有连接线 */
      .timeline-item::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 16px;
        bottom: -30px;
        width: 4px;
        background: rgba(68, 89, 120, 1);
        transform: translateX(-50%);
        z-index: 1;
      }

      .transportation-item::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 12px;
        bottom: -30px;
        width: 4px;
        background: rgba(68, 89, 120, 1);
        transform: translateX(-50%);
        z-index: 1;
      }

      .transportation-item::before {
        content: "";
        position: absolute;
        left: 50%;
        top: -12px;
        height: 30px;
        width: 4px;
        background: rgba(68, 89, 120, 1);
        transform: translateX(-50%);
        z-index: 1;
      }

      .timeline-item:last-child::after {
        display: none;
      }

      .transportation-dot {
        position: absolute;
        left: 50%;
        top: -17px;
        width: 12px;
        height: 12px;
        background: #ed8936;
        border-radius: 50%;
        transform: translateX(-50%);
        z-index: 3;
        box-shadow: 0 0 0 4px rgba(237, 137, 54, 0.2);
      }

      .timeline-content {
        width: 100%;
        padding: 20px 0;
        position: relative;
        z-index: 2;
      }

      .timeline-item {
        display: flex;
        justify-content: space-between;
        position: relative;
        margin-bottom: 30px;
      }

      .timeline-dot {
        position: absolute;
        left: 50%;
        top: 0;
        width: 16px;
        height: 16px;
        background: #4fd1c5;
        border-radius: 50%;
        transform: translateX(-50%);
        z-index: 3;
        box-shadow: 0 0 0 4px rgba(79, 209, 197, 0.2);
      }

      .timeline-time {
        position: absolute;
        background: rgba(26, 54, 93, 0.8);
        color: white;
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        white-space: nowrap;
        z-index: 5;
      }

      /* 当卡片在左侧时，时间显示在右侧 */
      .timeline-card.left + .timeline-time {
        left: 57%;
        top: 0px;
      }

      /* 当卡片在右侧时，时间显示在左侧 */
      .timeline-card.right + .timeline-time {
        right: 57%;
        top: 0px;
      }

      .timeline-card {
        width: 42%;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(255, 255, 255, 0.7) 100%
        );
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .timeline-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }

      .timeline-card.left {
        margin-right: auto;
      }

      .timeline-card.right {
        margin-left: auto;
      }

      .timeline-card.left:after,
      .timeline-card.right:after {
        content: "";
        position: absolute;
        top: 15px;
        width: 12px;
        height: 12px;
        background: inherit;
        transform: rotate(45deg);
      }

      .timeline-card.left:after {
        right: -6px;
      }

      .timeline-card.right:after {
        left: -6px;
      }

      .timeline-card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1a365d;
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .timeline-card-title .expand-icon {
        font-size: 18px;
        color: #4a5568;
        transition: transform 0.3s ease;
      }

      .timeline-card.expanded .expand-icon {
        transform: rotate(180deg);
      }

      .timeline-card-content {
        font-size: 14px;
        color: #4a5568;
        line-height: 1.5;
      }

      .timeline-card-details {
        height: 0;
        overflow: hidden;
        opacity: 0;
        transition: height 0.3s ease, opacity 0.3s ease;
      }

      .timeline-card.expanded .timeline-card-details {
        height: auto;
        opacity: 1;
      }

      .timeline-card-meta {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        font-size: 12px;
        color: #718096;
      }

      .timeline-card-image {
        width: 100%;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;
        margin-top: 10px;
      }

      .timeline-card-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .transportation-item {
        display: flex;
        justify-content: center;
        position: relative;
        margin: 25px 0;
        z-index: 2;
      }

      .transportation {
        position: relative;
        left: 0;
        margin-top: 15px;
        background: rgba(255, 255, 255, 0.8);
        padding: 4px 6px;
        border-radius: 10px;
        font-size: 12px;
        color: #4a5568;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        gap: 1px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        z-index: 4;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: min-content;
        width: auto;
        min-height: 24px;
        height: auto;
        overflow: hidden;
      }

      .transportation.expanded {
        width: auto;
        min-width: 100px;
        max-width: 150px;
        height: auto;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
      }

      .transportation:hover {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
      }

      .transportation-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
        width: auto;
        min-height: 20px;
        white-space: nowrap;
        padding: 0;
      }

      .transportation-icon {
        font-size: 14px;
        color: #1a365d;
      }

      .transportation-time {
        font-weight: 600;
        font-size: 11px;
        color: #1a365d;
        white-space: nowrap;
      }

      .transportation-detail {
        font-size: 10px;
        color: #718096;
        text-align: center;
        line-height: 1.2;
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        transition: opacity 0.3s ease, max-height 0.3s ease;
        width: 100%;
        margin-top: 0;
        white-space: normal;
        padding: 0;
        word-break: break-word;
      }

      .transportation.expanded .transportation-detail {
        opacity: 1;
        max-height: 100px;
        margin-top: 4px;
      }

      /* 添加展开指示器 */
      .transportation-header::after {
        content: "▼";
        font-size: 10px;
        color: #4a5568;
        margin-left: 4px;
        transition: transform 0.3s ease;
      }

      .transportation.expanded .transportation-header::after {
        content: "▲";
      }

      .list-item {
        display: flex;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.8) 0%,
          rgba(255, 255, 255, 0.6) 100%
        );
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .list-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }

      .item-content {
        flex: 1;
        padding: 16px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .item-title {
        font-size: 18px;
        font-weight: 600;
        color: #1a365d;
        margin-bottom: 8px;
      }

      .item-description {
        font-size: 14px;
        color: #4a5568;
        line-height: 1.5;
        margin-bottom: 12px;
      }

      .item-meta {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 12px;
        color: #718096;
      }

      .item-meta span {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .item-image {
        width: 180px;
        min-width: 180px;
        height: 140px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(
          135deg,
          rgba(111, 121, 133, 0.1) 0%,
          rgba(144, 156, 170, 0.1) 50%,
          rgba(209, 194, 189, 0.1) 100%
        );
      }

      .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      .list-item:hover .item-image img {
        transform: scale(1.05);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="main">
        <header>
          <div class="logo">
            <img
              src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzFhMzY1ZCIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xLjQxIDEwLjA5YzEuMDcgMCAxLjkzLS44NiAxLjkzLTEuOTNzLS44Ni0xLjkzLTEuOTMtMS45My0xLjkzLjg2LTEuOTMgMS45My44NiAxLjkzIDEuOTMgMS45M3ptNi40MSA1LjI5Yy0uMTQtLjQtLjc2LS43LTEuMTktLjc5LS40My0uMDktMi45NS0uMTEtMy40Ny0uMTEtLjUyIDAtMy4wNC4wMi0zLjQ3LjExLS40My4wOS0xLjA1LjM5LTEuMTkuNzktLjE0LjQtLjI2IDEuMzUtLjI2IDEuNzQgMCAuMzkuMTIgMS4zNC4yNiAxLjc0LjE0LjQuNzYuNyAxLjE5Ljc5LjQzLjA5IDIuOTUuMTEgMy40Ny4xMS41MiAwIDMuMDQtLjAyIDMuNDctLjExLjQzLS4wOSAxLjA1LS4zOSAxLjE5LS43OS4xNC0uNC4yNi0xLjM1LjI2LTEuNzQgMC0uMzktLjEyLTEuMzQtLjI2LTEuNzR6bS0xLjg4LTcuMzZjMS4wNyAwIDEuOTMtLjg2IDEuOTMtMS45M3MtLjg2LTEuOTMtMS45My0xLjkzLTEuOTMuODYtMS45MyAxLjkzLjg2IDEuOTMgMS45MyAxLjkzeiIvPjwvc3ZnPg=="
              alt="穷游旅行"
            />
            <span style="margin-left: 8px; color: #1a365d; font-weight: bold"
              >穷游旅行</span
            >
          </div>
          <div class="nav-links">
            <a href="#">首页</a>
            <a href="#">价格</a>
            <a href="#" class="login-btn">登录</a>
          </div>
        </header>

        <div class="content">
          <h1 class="main-title">你的穷游旅行规划</h1>

          <div class="origin-input-container">
            <div class="origin-input">
              <button>↺</button>
              <input
                type="input"
                style="text-align: center"
                value="杭州"
                readonly
              />
              <button>→</button>
            </div>
          </div>

          <div class="inbox-container">
            <div class="inbox-header">
              <div class="steps-container">
                <div class="step active">
                  <div class="step-number">1</div>
                  <div class="step-text">选择目的地</div>
                </div>
                <div class="step-divider"></div>
                <div class="step in-progress">
                  <div class="step-number">2</div>
                  <div class="step-text">选择时间</div>
                </div>
                <div class="step-divider"></div>
                <div class="step">
                  <div class="step-number">3</div>
                  <div class="step-text">计划安排</div>
                </div>
              </div>
              <button class="refresh-btn">关闭</button>
            </div>
            <div class="inbox-content">
              <!-- 目的地选择页面 -->
              <div class="content-page active" id="step1-content">
                <div class="item-list">
                  <div class="list-item">
                    <div class="item-content">
                      <div>
                        <div class="item-title">日本东京</div>
                        <div class="item-description">
                          东京是一座充满活力的城市，融合了传统文化与现代科技。这里有美食、购物、历史景点和先进科技，适合各类旅行者。
                        </div>
                      </div>
                      <div class="item-meta">
                        <span>⭐ 4.8</span>
                        <span>💰 中等</span>
                        <span>🕒 建议停留 5-7 天</span>
                      </div>
                    </div>
                    <div class="item-image">
                      <img
                        src="https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                        alt="东京"
                      />
                    </div>
                  </div>

                  <div class="list-item">
                    <div class="item-content">
                      <div>
                        <div class="item-title">泰国清迈</div>
                        <div class="item-description">
                          清迈是泰国北部的文化中心，以其古老的寺庙、传统手工艺和美食而闻名。这里的生活成本较低，是背包客和数字游民的热门目的地。
                        </div>
                      </div>
                      <div class="item-meta">
                        <span>⭐ 4.6</span>
                        <span>💰 低</span>
                        <span>🕒 建议停留 3-5 天</span>
                      </div>
                    </div>
                    <div class="item-image">
                      <img
                        src="https://images.unsplash.com/photo-1528181304800-259b08848526?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                        alt="清迈"
                      />
                    </div>
                  </div>

                  <div class="list-item">
                    <div class="item-content">
                      <div>
                        <div class="item-title">越南岘港</div>
                        <div class="item-description">
                          岘港拥有美丽的海滩、独特的建筑和丰富的文化遗产。这里的物价较为亲民，是越南中部的旅游热点，适合寻求海滩度假的旅行者。
                        </div>
                      </div>
                      <div class="item-meta">
                        <span>⭐ 4.5</span>
                        <span>💰 低</span>
                        <span>🕒 建议停留 3-4 天</span>
                      </div>
                    </div>
                    <div class="item-image">
                      <img
                        src="https://images.unsplash.com/photo-1559592413-7cec4d0cae2b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                        alt="岘港"
                      />
                    </div>
                  </div>

                  <div class="list-item">
                    <div class="item-content">
                      <div>
                        <div class="item-title">马来西亚槟城</div>
                        <div class="item-description">
                          槟城以其殖民地建筑、街头艺术和美食而著名。这座城市融合了马来、中国和印度文化，是一个多元文化的熔炉，特别适合美食爱好者。
                        </div>
                      </div>
                      <div class="item-meta">
                        <span>⭐ 4.7</span>
                        <span>💰 低至中等</span>
                        <span>🕒 建议停留 2-4 天</span>
                      </div>
                    </div>
                    <div class="item-image">
                      <img
                        src="https://images.unsplash.com/photo-1567157577867-05ccb1388e66?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                        alt="槟城"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 时间选择页面 -->
              <div class="content-page" id="step2-content">
                <div class="train-container">
                  <!-- 左右分栏的列表结构 -->
                  <div class="train-list-container">
                    <!-- 去程列表 -->
                    <div class="train-list train-list-left">
                      <h3
                        style="
                          margin-bottom: 15px;
                          color: #1a365d;
                          text-align: center;
                        "
                      >
                        去程
                      </h3>

                      <!-- 去程火车票项目 -->
                      <div class="train-item">
                        <div class="train-item-header">
                          <div>
                            <div class="train-time">08:30</div>
                            <div class="train-stations">北京南</div>
                          </div>
                          <div>
                            <div class="train-duration">5小时20分</div>
                            <div
                              style="
                                text-align: center;
                                font-size: 18px;
                                color: #718096;
                              "
                            >
                              →
                            </div>
                          </div>
                          <div>
                            <div class="train-time">13:50</div>
                            <div class="train-stations">上海虹桥</div>
                          </div>
                        </div>
                        <div class="train-info">
                          <div class="train-number">G105</div>
                          <div class="train-price">¥553</div>
                        </div>
                        <div class="train-tags">
                          <span class="train-tag">二等座: 有</span>
                          <span class="train-tag">一等座: 有</span>
                        </div>
                      </div>

                      <div class="train-item">
                        <div class="train-item-header">
                          <div>
                            <div class="train-time">09:15</div>
                            <div class="train-stations">北京南</div>
                          </div>
                          <div>
                            <div class="train-duration">4小时45分</div>
                            <div
                              style="
                                text-align: center;
                                font-size: 18px;
                                color: #718096;
                              "
                            >
                              →
                            </div>
                          </div>
                          <div>
                            <div class="train-time">14:00</div>
                            <div class="train-stations">上海虹桥</div>
                          </div>
                        </div>
                        <div class="train-info">
                          <div class="train-number">G19</div>
                          <div class="train-price">¥598</div>
                        </div>
                        <div class="train-tags">
                          <span class="train-tag">二等座: 有</span>
                          <span class="train-tag">一等座: 有</span>
                        </div>
                      </div>

                      <div class="train-item">
                        <div class="train-item-header">
                          <div>
                            <div class="train-time">10:00</div>
                            <div class="train-stations">北京南</div>
                          </div>
                          <div>
                            <div class="train-duration">5小时38分</div>
                            <div
                              style="
                                text-align: center;
                                font-size: 18px;
                                color: #718096;
                              "
                            >
                              →
                            </div>
                          </div>
                          <div>
                            <div class="train-time">15:38</div>
                            <div class="train-stations">上海虹桥</div>
                          </div>
                        </div>
                        <div class="train-info">
                          <div class="train-number">G13</div>
                          <div class="train-price">¥553</div>
                        </div>
                        <div class="train-tags">
                          <span class="train-tag">二等座: 有</span>
                          <span class="train-tag">一等座: 有</span>
                        </div>
                      </div>
                    </div>

                    <!-- 返程列表 -->
                    <div class="train-list">
                      <h3
                        style="
                          margin-bottom: 15px;
                          color: #1a365d;
                          text-align: center;
                        "
                      >
                        返程
                      </h3>

                      <!-- 返程火车票项目 -->
                      <div class="train-item">
                        <div class="train-item-header">
                          <div>
                            <div class="train-time">09:00</div>
                            <div class="train-stations">上海虹桥</div>
                          </div>
                          <div>
                            <div class="train-duration">4小时58分</div>
                            <div
                              style="
                                text-align: center;
                                font-size: 18px;
                                color: #718096;
                              "
                            >
                              →
                            </div>
                          </div>
                          <div>
                            <div class="train-time">13:58</div>
                            <div class="train-stations">北京南</div>
                          </div>
                        </div>
                        <div class="train-info">
                          <div class="train-number">G22</div>
                          <div class="train-price">¥553</div>
                        </div>
                        <div class="train-tags">
                          <span class="train-tag">二等座: 有</span>
                          <span class="train-tag">一等座: 有</span>
                        </div>
                      </div>

                      <div class="train-item">
                        <div class="train-item-header">
                          <div>
                            <div class="train-time">10:05</div>
                            <div class="train-stations">上海虹桥</div>
                          </div>
                          <div>
                            <div class="train-duration">5小时25分</div>
                            <div
                              style="
                                text-align: center;
                                font-size: 18px;
                                color: #718096;
                              "
                            >
                              →
                            </div>
                          </div>
                          <div>
                            <div class="train-time">15:30</div>
                            <div class="train-stations">北京南</div>
                          </div>
                        </div>
                        <div class="train-info">
                          <div class="train-number">G16</div>
                          <div class="train-price">¥598</div>
                        </div>
                        <div class="train-tags">
                          <span class="train-tag">二等座: 有</span>
                          <span class="train-tag">一等座: 有</span>
                        </div>
                      </div>

                      <div class="train-item">
                        <div class="train-item-header">
                          <div>
                            <div class="train-time">14:30</div>
                            <div class="train-stations">上海虹桥</div>
                          </div>
                          <div>
                            <div class="train-duration">5小时10分</div>
                            <div
                              style="
                                text-align: center;
                                font-size: 18px;
                                color: #718096;
                              "
                            >
                              →
                            </div>
                          </div>
                          <div>
                            <div class="train-time">19:40</div>
                            <div class="train-stations">北京南</div>
                          </div>
                        </div>
                        <div class="train-info">
                          <div class="train-number">G126</div>
                          <div class="train-price">¥553</div>
                        </div>
                        <div class="train-tags">
                          <span class="train-tag">二等座: 有</span>
                          <span class="train-tag">一等座: 有</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧日期选择器 -->
                  <div class="train-date-selector">
                    <div class="date-selector-header">
                      <div class="date-title">选择日期</div>
                    </div>
                    <div class="date-selector-content">
                      <div class="date-item" data-date="2023-07-15">
                        <div class="date-day">15</div>
                        <div class="date-weekday">周六</div>
                      </div>
                      <div class="date-item" data-date="2023-07-16">
                        <div class="date-day">16</div>
                        <div class="date-weekday">周日</div>
                      </div>
                      <div class="date-item" data-date="2023-07-17">
                        <div class="date-day">17</div>
                        <div class="date-weekday">周一</div>
                      </div>
                      <div class="date-item" data-date="2023-07-18">
                        <div class="date-day">18</div>
                        <div class="date-weekday">周二</div>
                      </div>
                      <div class="date-item" data-date="2023-07-19">
                        <div class="date-day">19</div>
                        <div class="date-weekday">周三</div>
                      </div>
                      <div class="date-item" data-date="2023-07-20">
                        <div class="date-day">20</div>
                        <div class="date-weekday">周四</div>
                      </div>
                      <div class="date-item" data-date="2023-07-21">
                        <div class="date-day">21</div>
                        <div class="date-weekday">周五</div>
                      </div>
                      <div class="date-item" data-date="2023-07-22">
                        <div class="date-day">22</div>
                        <div class="date-weekday">周六</div>
                      </div>
                      <div class="date-item" data-date="2023-07-23">
                        <div class="date-day">23</div>
                        <div class="date-weekday">周日</div>
                      </div>
                      <div class="date-item" data-date="2023-07-24">
                        <div class="date-day">24</div>
                        <div class="date-weekday">周一</div>
                      </div>
                      <div class="date-item" data-date="2023-07-25">
                        <div class="date-day">25</div>
                        <div class="date-weekday">周二</div>
                      </div>
                      <div class="date-item" data-date="2023-07-26">
                        <div class="date-day">26</div>
                        <div class="date-weekday">周三</div>
                      </div>
                      <div class="date-item" data-date="2023-07-27">
                        <div class="date-day">27</div>
                        <div class="date-weekday">周四</div>
                      </div>
                      <div class="date-item" data-date="2023-07-28">
                        <div class="date-day">28</div>
                        <div class="date-weekday">周五</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 计划安排页面 -->
              <div class="content-page" id="step3-content">
                <div class="itinerary-container">
                  <!-- 时间线容器 -->
                  <div class="timeline-container">
                    <!-- 时间线 -->
                    <div class="timeline"></div>

                    <!-- 时间线内容 -->
                    <div class="timeline-content">
                      <!-- 景点项目: 上海迎宾馆 -->
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-card left">
                          <div class="timeline-card-title">
                            <span>上海迎宾馆</span>
                            <span class="expand-icon">▼</span>
                          </div>
                          <div class="timeline-card-content">
                            上海迎宾馆是上海地标性建筑，融合了中西建筑风格，可以欣赏到独特的建筑艺术。
                          </div>
                          <div class="timeline-card-details">
                            <div class="timeline-card-meta">
                              <span>门票: 免费</span>
                              <span>推荐停留: 1.5小时</span>
                            </div>
                            <div class="timeline-card-image">
                              <img
                                src="https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                                alt="上海迎宾馆"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="timeline-time">09:00 - 10:30</div>
                      </div>

                      <!-- 交通方式 -->
                      <div class="transportation-item">
                        <div class="transportation-dot"></div>
                        <div class="transportation">
                          <div class="transportation-header">
                            <span class="transportation-icon">🚕</span>
                            <span class="transportation-time">15分钟</span>
                          </div>
                          <span class="transportation-detail"
                            >打车从迎宾馆南门到外滩观光隧道入口</span
                          >
                        </div>
                      </div>

                      <!-- 景点项目: 外滩 -->
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-card right">
                          <div class="timeline-card-title">
                            <span>外滩</span>
                            <span class="expand-icon">▼</span>
                          </div>
                          <div class="timeline-card-content">
                            外滩是上海最著名的商业街区之一，汇集了各类商场、餐厅和历史建筑。
                          </div>
                          <div class="timeline-card-details">
                            <div class="timeline-card-meta">
                              <span>门票: 免费</span>
                              <span>推荐停留: 2小时</span>
                            </div>
                            <div class="timeline-card-image">
                              <img
                                src="https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                                alt="外滩"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="timeline-time">11:00 - 13:00</div>
                      </div>

                      <!-- 交通方式 -->
                      <div class="transportation-item">
                        <div class="transportation-dot"></div>
                        <div class="transportation">
                          <div class="transportation-header">
                            <span class="transportation-icon">🚇</span>
                            <span class="transportation-time">20分钟</span>
                          </div>
                          <span class="transportation-detail"
                            >地铁2号线从南京东路站到人民广场站</span
                          >
                        </div>
                      </div>

                      <!-- 景点项目: 豪生花园 -->
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-card left">
                          <div class="timeline-card-title">
                            <span>豪生花园</span>
                            <span class="expand-icon">▼</span>
                          </div>
                          <div class="timeline-card-content">
                            豪生花园是上海最大的花园之一，园内植物种类丰富，环境幽雅，是休闲游玩的好去处。
                          </div>
                          <div class="timeline-card-details">
                            <div class="timeline-card-meta">
                              <span>门票: ¥50</span>
                              <span>推荐停留: 1.5小时</span>
                            </div>
                            <div class="timeline-card-image">
                              <img
                                src="https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80"
                                alt="豪生花园"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="timeline-time">13:30 - 15:00</div>
                      </div>

                      <!-- 交通方式 -->
                      <div class="transportation-item">
                        <div class="transportation-dot"></div>
                        <div class="transportation">
                          <div class="transportation-header">
                            <span class="transportation-icon">🚶</span>
                            <span class="transportation-time">10分钟</span>
                          </div>
                          <span class="transportation-detail"
                            >步行从豪生花园西门到田子坊北入口</span
                          >
                        </div>
                      </div>

                      <!-- 景点项目: 田子坊 -->
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-card right">
                          <div class="timeline-card-title">
                            <span>田子坊</span>
                            <span class="expand-icon">▼</span>
                          </div>
                          <div class="timeline-card-content">
                            田子坊是上海历史悠久的街区，保存了大量的石库门建筑，充满浓厚的文化氛围。
                          </div>
                          <div class="timeline-card-details">
                            <div class="timeline-card-meta">
                              <span>门票: 免费</span>
                              <span>推荐停留: 2小时</span>
                            </div>
                            <div class="timeline-card-image">
                              <img
                                src="https://images.unsplash.com/photo-1598511726623-d3e9f2db00e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                                alt="田子坊"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="timeline-time">15:30 - 17:30</div>
                      </div>

                      <!-- 交通方式 -->
                      <div class="transportation-item">
                        <div class="transportation-dot"></div>
                        <div class="transportation">
                          <div class="transportation-header">
                            <span class="transportation-icon">🚕</span>
                            <span class="transportation-time">25分钟</span>
                          </div>
                          <span class="transportation-detail"
                            >打车从田子坊南门到外滩观景平台</span
                          >
                        </div>
                      </div>

                      <!-- 景点项目: 外滩夜景 -->
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-card left">
                          <div class="timeline-card-title">
                            <span>外滩夜景</span>
                            <span class="expand-icon">▼</span>
                          </div>
                          <div class="timeline-card-content">
                            晚上的外滩灯火辨然，汇聚了世界各地的建筑风格，是欣赏上海夜景的最佳地点。
                          </div>
                          <div class="timeline-card-details">
                            <div class="timeline-card-meta">
                              <span>门票: 免费</span>
                              <span>推荐停留: 2小时</span>
                            </div>
                            <div class="timeline-card-image">
                              <img
                                src="https://images.unsplash.com/photo-1536599424071-0b215a388ba7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                                alt="外滩夜景"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="timeline-time">18:30 - 20:30</div>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧日期选择器 -->
                  <div class="day-selector">
                    <div class="day-selector-header">
                      <div class="day-title">行程日期</div>
                    </div>
                    <div class="day-selector-content">
                      <div class="day-item" data-date="2023-07-15">
                        <div class="day-number">1</div>
                        <div class="day-label">第一天</div>
                      </div>
                      <div class="day-item" data-date="2023-07-16">
                        <div class="day-number">2</div>
                        <div class="day-label">第二天</div>
                      </div>
                      <div class="day-item" data-date="2023-07-17">
                        <div class="day-number">3</div>
                        <div class="day-label">第三天</div>
                      </div>
                      <div class="day-item" data-date="2023-07-18">
                        <div class="day-number">4</div>
                        <div class="day-label">第四天</div>
                      </div>
                      <div class="day-item" data-date="2023-07-19">
                        <div class="day-number">5</div>
                        <div class="day-label">第五天</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 切换定价周期
      document.querySelectorAll(".toggle-btn").forEach((btn) => {
        btn.addEventListener("click", function () {
          document.querySelectorAll(".toggle-btn").forEach((b) => {
            b.classList.remove("active");
          });
          this.classList.add("active");
        });
      });

      // 页面加载时，将主标题和起始地输入框居中
      function centerElements() {
        const mainHeight = document.querySelector(".main").offsetHeight;
        const titleHeight = document.querySelector(".main-title").offsetHeight;
        const inputHeight = document.querySelector(
          ".origin-input-container"
        ).offsetHeight;
        const headerHeight = document.querySelector("header").offsetHeight;

        // 计算垂直居中的位置，考虑header的高度
        const totalHeight = titleHeight + inputHeight;
        const availableHeight = mainHeight - headerHeight;
        const topPosition =
          (availableHeight - totalHeight) / 2 - 2 * headerHeight;

        document.querySelector(
          ".main-title"
        ).style.transform = `translateY(${topPosition}px)`;
        document.querySelector(
          ".origin-input-container"
        ).style.transform = `translateY(${topPosition}px)`;
      }

      // 页面加载完成后执行居中
      window.addEventListener("load", centerElements);
      window.addEventListener("resize", centerElements);

      // 初始状态标记
      let inboxVisible = false;

      // 显示内容
      function showInbox() {
        if (inboxVisible) return;

        const inboxContainer = document.querySelector(".inbox-container");
        inboxContainer.style.display = "block";

        // 使用setTimeout确保display:block生效后再添加动画
        setTimeout(() => {
          inboxContainer.style.opacity = "1";
          inboxContainer.style.transform = "translateY(0)";

          // 移动标题和输入框到顶部
          document.querySelector(".main-title").style.transform =
            "translateY(0)";
          document.querySelector(".origin-input-container").style.transform =
            "translateY(0)";

          inboxVisible = true;
        }, 50);
      }

      // 隐藏内容
      function hideInbox() {
        if (!inboxVisible) return;

        const inboxContainer = document.querySelector(".inbox-container");
        inboxContainer.style.opacity = "0";
        inboxContainer.style.transform = "translateY(20px)";

        // 恢复标题和输入框到中间位置
        centerElements();

        // 等待动画完成后隐藏元素
        setTimeout(() => {
          inboxContainer.style.display = "none";
          inboxVisible = false;
        }, 800);
      }

      // 关闭按钮点击事件
      document
        .querySelector(".refresh-btn")
        .addEventListener("click", hideInbox);

      // 清空起始地和打开内容
      document
        .querySelectorAll(".origin-input button")
        .forEach((btn, index) => {
          btn.addEventListener("click", function () {
            // 如果是右箭头按钮（第二个按钮）
            if (index === 1) {
              showInbox();
            } else {
              // 清空功能（第一个按钮）
              const input = this.parentElement.querySelector("input");
              input.value = ``;
            }
          });
        });

      // 步骤条点击事件
      document.querySelectorAll(".step").forEach((step, index) => {
        step.addEventListener("click", function () {
          // 更新步骤状态
          document.querySelectorAll(".step").forEach((s, i) => {
            if (i === index) {
              s.classList.add("active");
              s.classList.remove("in-progress");
            } else if (i < index) {
              s.classList.add("active");
              s.classList.remove("in-progress");
            } else {
              s.classList.remove("active");
              s.classList.remove("in-progress");
            }

            // 下一个步骤标记为进行中
            if (i === index + 1) {
              s.classList.add("in-progress");
            }
          });

          // 切换内容页面
          document.querySelectorAll(".content-page").forEach((page, i) => {
            if (i === index) {
              // 先隐藏所有页面
              document.querySelectorAll(".content-page").forEach((p) => {
                p.style.display = "none";
              });

              // 然后显示当前页面
              page.style.opacity = "0";
              page.style.transform = "translateX(20px)";
              page.style.display = "block";

              // 强制重绘
              void page.offsetWidth;

              // 添加动画效果
              setTimeout(() => {
                page.style.opacity = "1";
                page.style.transform = "translateX(0)";
              }, 50);

              // 如果是时间选择页面，禁用inbox-content的滚动
              const inboxContent = document.querySelector(".inbox-content");
              if (inboxContent) {
                if (i === 1) {
                  // 时间选择页面索引为1
                  inboxContent.style.overflow = "hidden";
                } else {
                  inboxContent.style.overflowY = "auto";
                }
              }
            }
          });
        });
      });

      // 日期选择器交互
      let selectedDates = [];

      document.addEventListener("DOMContentLoaded", function () {
        // 默认选中第一个日期
        const firstDateItem = document.querySelector(
          ".date-selector-content .date-item"
        );
        if (firstDateItem) {
          firstDateItem.classList.add("selected");
          firstDateItem.classList.add("range-start");
          selectedDates.push(firstDateItem.getAttribute("data-date"));
        }

        // 默认选中两列中的第一个车次
        const firstGoTrainItem = document.querySelector(
          ".train-list-left .train-item"
        );
        const firstReturnTrainItem = document.querySelector(
          ".train-list:not(.train-list-left) .train-item"
        );

        if (firstGoTrainItem) {
          firstGoTrainItem.classList.add("selected");
        }

        if (firstReturnTrainItem) {
          firstReturnTrainItem.classList.add("selected");
        }

        // 为所有火车票项目添加点击事件
        document.querySelectorAll(".train-item").forEach((item) => {
          item.addEventListener("click", function () {
            // 获取当前列表（去程或返程）
            const isList = this.closest(".train-list");
            const isGoList = isList.classList.contains("train-list-left");

            // 移除同一列表中其他项目的选中状态
            const selector = isGoList
              ? ".train-list-left .train-item"
              : ".train-list:not(.train-list-left) .train-item";
            document.querySelectorAll(selector).forEach((el) => {
              el.classList.remove("selected");
            });

            // 添加当前项目的选中状态
            this.classList.add("selected");
          });
        });

        // 为所有日期项添加点击事件
        document
          .querySelectorAll(".date-selector-content .date-item")
          .forEach((item) => {
            item.addEventListener("click", function () {
              const date = this.getAttribute("data-date");

              // 如果已经选了两个日期，重置选择
              if (selectedDates.length === 2) {
                selectedDates = [];
                document.querySelectorAll(".date-item").forEach((el) => {
                  el.classList.remove("selected");
                  el.classList.remove("range-start");
                  el.classList.remove("range-end");
                  el.classList.remove("in-range");
                });
              }

              // 选择当前日期
              this.classList.add("selected");
              selectedDates.push(date);

              // 标记范围开始和结束
              if (selectedDates.length === 1) {
                this.classList.add("range-start");
              } else if (selectedDates.length === 2) {
                this.classList.add("range-end");

                // 对日期进行排序，确保开始日期在前
                selectedDates.sort();

                // 标记范围内的日期
                markDateRange(selectedDates[0], selectedDates[1]);

                // 更新火车票信息显示
                updateTrainDisplay(selectedDates[0], selectedDates[1]);
              }
            });
          });
      });

      // 标记日期范围
      function markDateRange(startDate, endDate) {
        const dateItems = document.querySelectorAll(
          ".date-selector-content .date-item"
        );
        const start = new Date(startDate);
        const end = new Date(endDate);

        dateItems.forEach((item) => {
          const itemDate = new Date(item.getAttribute("data-date"));

          // 如果日期在范围内但不是开始或结束日期
          if (itemDate > start && itemDate < end) {
            item.classList.add("in-range");
          }
        });
      }

      // 更新火车票显示
      function updateTrainDisplay(startDate, endDate) {
        // 在实际应用中，这里可以发送API请求获取对应日期的火车票信息
        // 这里我们只是模拟更新显示

        // 更新去程和返程标题
        const goTitle = document.querySelector(".train-list-left h3");
        const returnTitle = document.querySelector(
          ".train-list:not(.train-list-left) h3"
        );

        if (goTitle && returnTitle) {
          const startDateObj = new Date(startDate);
          const endDateObj = new Date(endDate);

          const formatDate = (date) => {
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${month}月${day}日`;
          };

          goTitle.textContent = `去程 (${formatDate(startDateObj)})`;
          returnTitle.textContent = `返程 (${formatDate(endDateObj)})`;

          // 添加一个简单的动画效果
          document.querySelectorAll(".train-item").forEach((item) => {
            item.style.opacity = "0.5";
            setTimeout(() => {
              item.style.opacity = "1";
            }, 300);
          });
        }
      }

      // 计划安排模块交互功能
      document.addEventListener("DOMContentLoaded", function () {
        // 默认选中第一个日期
        const firstDayItem = document.querySelector(
          ".day-selector-content .day-item"
        );
        if (firstDayItem) {
          firstDayItem.classList.add("selected");
        }

        // 景点卡片展开/收起功能
        document.querySelectorAll(".timeline-card").forEach((card) => {
          // 双击展开/收起
          card.addEventListener("dblclick", function () {
            this.classList.toggle("expanded");

            // 更新展开图标
            const expandIcon = this.querySelector(".expand-icon");
            if (expandIcon) {
              if (this.classList.contains("expanded")) {
                expandIcon.textContent = "▲";
              } else {
                expandIcon.textContent = "▼";
              }
            }
          });

          // 点击展开图标也可以展开/收起
          const expandIcon = card.querySelector(".expand-icon");
          if (expandIcon) {
            expandIcon.addEventListener("click", function (e) {
              e.stopPropagation(); // 阻止事件冒泡
              const parentCard = this.closest(".timeline-card");
              parentCard.classList.toggle("expanded");

              // 更新图标
              if (parentCard.classList.contains("expanded")) {
                this.textContent = "▲";
              } else {
                this.textContent = "▼";
              }
            });
          }
        });

        // 交通方式点击事件
        document.querySelectorAll(".transportation").forEach((item) => {
          item.addEventListener("click", function () {
            // 切换展开/收起状态
            this.classList.toggle("expanded");
          });
        });

        // 日期选择功能
        document
          .querySelectorAll(".day-selector-content .day-item")
          .forEach((item) => {
            item.addEventListener("click", function () {
              // 移除所有选中状态
              document
                .querySelectorAll(".day-selector-content .day-item")
                .forEach((el) => {
                  el.classList.remove("selected");
                });

              // 添加当前项目的选中状态
              this.classList.add("selected");

              // 模拟更新时间线内容
              const dayNumber = this.querySelector(".day-number").textContent;
              updateItinerary(dayNumber);
            });
          });
      });

      // 模拟更新时间线内容
      function updateItinerary(dayNumber) {
        // 在实际应用中，这里可以发送API请求获取对应日期的行程安排
        // 这里我们只是模拟更新显示

        // 添加一个简单的动画效果
        const timelineContent = document.querySelector(".timeline-content");
        if (timelineContent) {
          timelineContent.style.opacity = "0.5";

          setTimeout(() => {
            // 这里可以根据实际情况更新时间线内容
            // 我们这里只是模拟更新标题
            document
              .querySelectorAll(".timeline-time")
              .forEach((timeEl, index) => {
                // 根据天数简单调整时间，仅作演示
                const times = [
                  [
                    "09:00 - 10:30",
                    "11:00 - 13:00",
                    "13:30 - 15:00",
                    "15:30 - 17:30",
                    "18:30 - 20:30",
                  ], // 第一天
                  [
                    "08:30 - 10:00",
                    "10:30 - 12:30",
                    "13:00 - 14:30",
                    "15:00 - 17:00",
                    "18:00 - 20:00",
                  ], // 第二天
                  [
                    "09:30 - 11:00",
                    "11:30 - 13:30",
                    "14:00 - 15:30",
                    "16:00 - 18:00",
                    "19:00 - 21:00",
                  ], // 第三天
                  [
                    "10:00 - 11:30",
                    "12:00 - 14:00",
                    "14:30 - 16:00",
                    "16:30 - 18:30",
                    "19:30 - 21:30",
                  ], // 第四天
                  [
                    "08:00 - 09:30",
                    "10:00 - 12:00",
                    "12:30 - 14:00",
                    "14:30 - 16:30",
                    "17:00 - 19:00",
                  ], // 第五天
                ];

                if (times[dayNumber - 1] && times[dayNumber - 1][index]) {
                  timeEl.textContent = times[dayNumber - 1][index];
                }
              });

            timelineContent.style.opacity = "1";
          }, 300);
        }
      }
    </script>
  </body>
</html>
