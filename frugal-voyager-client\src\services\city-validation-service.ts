/**
 * 城市验证服务
 * 用于验证用户输入的城市名称是否有效
 */

// 城市数据类型
interface CityData {
  Name: string;
  StationCode: string;
  PingYin: string;
  PingYinShort: string;
}

// 城市数据缓存
let cityDataCache: CityData[] | null = null;

/**
 * 加载城市数据
 * @returns Promise<CityData[]> 城市数据数组
 */
export async function loadCityData(): Promise<CityData[]> {
  if (cityDataCache) {
    return cityDataCache;
  }

  try {
    const response = await fetch('/data/TrainAllStation.json');
    if (!response.ok) {
      throw new Error(`Failed to load city data: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    cityDataCache = data;
    return data;
  } catch (error) {
    console.error('Error loading city data:', error);
    return [];
  }
}

/**
 * 验证城市名称是否有效
 * @param cityName 城市名称
 * @returns Promise<boolean> 是否有效
 */
export async function validateCity(cityName: string): Promise<boolean> {
  if (!cityName || cityName.trim() === '') {
    return false;
  }

  const cityData = await loadCityData();
  
  // 检查城市名称是否在列表中
  return cityData.some(city => city.Name === cityName.trim());
}

/**
 * 获取所有有效的城市名称
 * @returns Promise<string[]> 城市名称数组
 */
export async function getAllCityNames(): Promise<string[]> {
  const cityData = await loadCityData();
  return cityData.map(city => city.Name);
}
