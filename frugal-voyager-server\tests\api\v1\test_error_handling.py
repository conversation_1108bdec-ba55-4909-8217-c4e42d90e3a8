"""
错误处理测试。

测试API的错误处理机制。
"""

import pytest
import json
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from app.utils.exceptions import ServiceException, ResourceNotFoundException
from app.schemas.errors import ErrorCode


def test_validation_error(client: TestClient):
    """测试请求验证错误。"""
    # 发送无效的请求（缺少必要字段）
    response = client.post(
        "/api/v1/cities/recommend",
        json={},  # 缺少position字段
    )

    # 验证响应
    assert response.status_code == 400
    data = response.json()
    assert data["status_code"] == 400
    assert data["message"] == "请求参数无效"
    assert data["error_code"] == ErrorCode.INVALID_REQUEST
    assert isinstance(data["details"], list)
    assert len(data["details"]) > 0
    assert "loc" in data["details"][0]
    assert "msg" in data["details"][0]
    assert "type" in data["details"][0]


def test_resource_not_found(client: TestClient):
    """测试资源未找到错误。"""
    # 模拟TicketService抛出ResourceNotFoundException
    with patch(
        "app.services.ticket_service.TicketService.get_available_tickets"
    ) as mock_get:
        mock_get.side_effect = ResourceNotFoundException(
            detail="未找到城市对应的车站", error_code=ErrorCode.CITY_NOT_FOUND
        )

        # 发送请求
        response = client.post(
            "/api/v1/tickets/search",
            json={"city_name": "不存在的城市", "departure_date": "2023-07-15"},
        )

        # 验证响应
        assert response.status_code == 404
        data = response.json()
        assert data["status_code"] == 404
        assert data["message"] == "未找到城市对应的车站"
        assert data["error_code"] == ErrorCode.CITY_NOT_FOUND


def test_service_exception(client: TestClient):
    """测试服务异常。"""
    # 模拟CityService抛出ServiceException
    with patch(
        "app.services.city_service.CityService.get_ai_city_recommendations"
    ) as mock_get:
        mock_get.side_effect = ServiceException(
            detail="AI服务暂时不可用", error_code=ErrorCode.AI_SERVICE_ERROR
        )

        # 发送请求
        response = client.post(
            "/api/v1/cities/recommend",
            json={"position": "杭州"},
        )

        # 验证响应
        assert response.status_code == 500
        data = response.json()
        assert data["status_code"] == 500
        assert data["message"] == "AI服务暂时不可用"
        assert data["error_code"] == ErrorCode.AI_SERVICE_ERROR


def test_mcp_service_error(client: TestClient):
    """测试MCP服务错误。"""
    # 模拟MCPService抛出ServiceException
    with patch(
        "app.services.mcp_service.MCPService.generate_travel_guide_items"
    ) as mock_generate:
        mock_generate.side_effect = ServiceException(
            detail="生成旅游指南失败", error_code=ErrorCode.MCP_SERVICE_ERROR
        )

        # 发送请求
        response = client.post(
            "/api/v1/travel-guides/generate-guide",
            json={"city": "杭州", "days": 2},
        )

        # 验证响应
        assert response.status_code == 500
        data = response.json()
        assert data["status_code"] == 500
        assert data["message"] == "生成旅游指南失败"
        assert data["error_code"] == ErrorCode.MCP_SERVICE_ERROR


def test_unhandled_exception(client: TestClient):
    """测试未处理的异常。"""
    # 由于我们不能轻易修改现有端点，我们将直接测试API响应

    # 创建一个测试路由，它会抛出一个未处理的异常
    # 我们可以通过修改现有的测试来验证错误处理

    # 模拟一个会抛出原始异常的服务
    with patch(
        "app.api.v1.endpoints.cities.CityService.get_ai_city_recommendations"
    ) as mock_get:
        # 设置mock抛出一个原始异常，这将被端点的try-except捕获并转换为ServiceException
        mock_get.side_effect = Exception("这是一个未处理的异常")

        # 发送请求
        response = client.post(
            "/api/v1/cities/recommend",
            json={"position": "杭州"},
        )

        # 验证响应
        assert response.status_code == 500
        data = response.json()
        assert data["status_code"] == 500
        # 由于我们的端点将异常转换为了ServiceException，所以消息不是"服务器内部错误"
        assert "生成AI城市推荐" in data["message"]
        assert data["error_code"] == ErrorCode.AI_SERVICE_ERROR
