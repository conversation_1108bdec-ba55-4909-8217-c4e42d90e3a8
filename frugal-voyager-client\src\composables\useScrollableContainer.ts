import { ref, onMounted, onUnmounted } from "vue";

/**
 * Scrollable container management
 * Handles scrollable container behavior and state
 *
 * @param options Configuration options
 * @returns Scrollable container utilities
 */
export function useScrollableContainer(
  options: {
    hideScrollbar?: boolean;
    autoScrollToTop?: boolean;
    onScroll?: (event: Event) => void;
  } = {}
) {
  const { hideScrollbar = true, autoScrollToTop = true, onScroll } = options;

  // Container reference
  const containerRef = ref<HTMLElement | null>(null);

  // Scroll state
  const scrollPosition = ref(0);
  const isScrolling = ref(false);

  /**
   * Handle scroll event
   * @param event Scroll event
   */
  const handleScroll = (event: Event) => {
    if (!containerRef.value) return;

    // Update scroll position
    scrollPosition.value = containerRef.value.scrollTop;

    // Set scrolling state
    isScrolling.value = true;

    // Clear scrolling state after a delay
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      isScrolling.value = false;
    }, 150);

    // Call onScroll callback if provided
    if (onScroll) {
      onScroll(event);
    }
  };

  // Timeout for scroll end detection
  let scrollTimeout: number;

  /**
   * Scroll to top
   * @param smooth Whether to use smooth scrolling
   */
  const scrollToTop = (smooth = true) => {
    if (!containerRef.value) return;

    if (smooth) {
      containerRef.value.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      containerRef.value.scrollTop = 0;
    }
  };

  /**
   * Scroll to bottom
   * @param smooth Whether to use smooth scrolling
   */
  const scrollToBottom = (smooth = true) => {
    if (!containerRef.value) return;

    if (smooth) {
      containerRef.value.scrollTo({
        top: containerRef.value.scrollHeight,
        behavior: "smooth",
      });
    } else {
      containerRef.value.scrollTop = containerRef.value.scrollHeight;
    }
  };

  /**
   * Scroll to element
   * @param element Element to scroll to
   * @param smooth Whether to use smooth scrolling
   * @param offset Offset from the top of the element
   */
  const scrollToElement = (element: HTMLElement, smooth = true, offset = 0) => {
    if (!containerRef.value || !element) return;

    const elementTop = element.offsetTop;

    if (smooth) {
      containerRef.value.scrollTo({
        top: elementTop - offset,
        behavior: "smooth",
      });
    } else {
      containerRef.value.scrollTop = elementTop - offset;
    }
  };

  /**
   * Enable or disable scrolling
   * @param enabled Whether scrolling is enabled
   */
  const setScrollingEnabled = (enabled: boolean) => {
    if (!containerRef.value) return;

    if (enabled) {
      containerRef.value.style.overflowY = "auto";
    } else {
      containerRef.value.style.overflowY = "hidden";
    }
  };

  // Setup and cleanup
  onMounted(() => {
    if (containerRef.value) {
      // Add scroll event listener
      containerRef.value.addEventListener("scroll", handleScroll);

      // Apply hide scrollbar style if needed
      if (hideScrollbar) {
        containerRef.value.style.scrollbarWidth = "none";
        // 使用 setAttribute 设置 MS 特定的样式属性
        containerRef.value.setAttribute(
          "style",
          containerRef.value.getAttribute("style") ||
            "" + "-ms-overflow-style: none;"
        );
      }

      // Auto scroll to top if needed
      if (autoScrollToTop) {
        scrollToTop(false);
      }
    }
  });

  onUnmounted(() => {
    // Remove scroll event listener
    if (containerRef.value) {
      containerRef.value.removeEventListener("scroll", handleScroll);
    }

    // Clear timeout
    clearTimeout(scrollTimeout);
  });

  return {
    containerRef,
    scrollPosition,
    isScrolling,
    scrollToTop,
    scrollToBottom,
    scrollToElement,
    setScrollingEnabled,
  };
}
