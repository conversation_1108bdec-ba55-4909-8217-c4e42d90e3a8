@use "@/assets/scss/components/shared-components" as *;
@use "@/assets/scss/utils/scrollbars" as *;

// Component-specific overrides can be added here if needed
.list-item {
  background: var(--gradient-card);
  position: relative; // 添加相对定位
  min-height: 140px; // 设置最小高度
}

// 使用更高优先级的选择器覆盖共享样式
.list-item .item-content {
  padding-right: 200px !important; // 为右侧图片留出空间
  width: 100% !important; // 确保内容区域占满宽度
  flex: 1 !important; // 确保内容区域可以伸展
  box-sizing: border-box !important; // 确保padding不会增加宽度
}

.item-image {
  width: 180px;
  min-width: 180px;
  position: absolute; // 使用绝对定位
  top: 0;
  right: 0;
  bottom: 0; // 延伸到底部
  height: 100%; // 占据整个高度
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    rgba(111, 121, 133, 0.1) 0%,
    rgba(144, 156, 170, 0.1) 50%,
    rgba(209, 194, 189, 0.1) 100%
  );

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }
}

.list-item:hover .item-image img {
  transform: scale(1.05);
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.spinner-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid rgba(26, 54, 93, 0.1);
  border-top-color: #1a365d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-circle-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border: 4px solid rgba(26, 54, 93, 0.1);
  border-top-color: #4a6fa5;
  border-radius: 50%;
  animation: spin 0.8s linear infinite reverse;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
  color: #1a365d;
  font-weight: 500;
}

// 活动信息样式
.item-events {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed rgba(26, 54, 93, 0.2);
}

.events-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 5px;
}

.event-item {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 13px;
  color: #4a5568;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
  color: #718096;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.refresh-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #4a6fa5 0%, #1a365d 100%);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(26, 54, 93, 0.1);
  }
}
