import type { AxiosError } from "axios";
import { getUserFriendlyErrorMessage } from "./errorCodes";

/**
 * 自定义API错误类
 */
export class ApiError extends Error {
  /** HTTP状态码 */
  status: number;

  /** 错误数据 */
  data: any;

  /** 错误代码 */
  errorCode?: string;

  /** 标识这是一个API错误 */
  isApiError: boolean;

  constructor(message: string, status: number, data?: any, errorCode?: string) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.data = data;
    this.errorCode = errorCode;
    this.isApiError = true;
  }

  /**
   * 获取用户友好的错误消息
   * 不暴露具体的技术细节
   */
  getUserFriendlyMessage(): string {
    return getUserFriendlyErrorMessage(this.errorCode);
  }
}

/**
 * 处理来自axios的API错误
 * @param error - axios错误
 * @returns 标准化的ApiError
 */
export function handleApiError(error: AxiosError): ApiError {
  if (error.response) {
    // 请求已发送，服务器返回了状态码
    // 但状态码超出了2xx的范围
    const status = error.response.status;
    const data = error.response.data as Record<string, any>;

    // 从响应中提取错误代码
    const errorCode = data?.error_code;

    // 使用通用错误消息，不暴露具体的技术细节
    let message = "操作失败，请稍后再试";

    // 处理常见的HTTP状态码
    switch (status) {
      case 400:
        message = "请求参数错误";
        break;
      case 401:
        message = "未授权，请登录";
        break;
      case 403:
        message = "拒绝访问";
        break;
      case 404:
        message = "请求的资源不存在";
        break;
      case 500:
        message = "服务暂时不可用，请稍后再试";
        break;
      default:
        message = `请求失败`;
    }

    // 如果服务器返回了特定的错误消息，记录它，但不直接显示给用户
    // 在开发环境中可以考虑使用服务器返回的消息进行调试
    if (data && typeof data.message === "string") {
      // 记录错误消息，但不直接显示给用户
      console.error(`API错误: ${data.message}`);

      // 在开发环境中可以使用服务器返回的消息
      // 使用import.meta.env代替process.env
      if (import.meta.env.DEV) {
        message = data.message;
      }
    }

    return new ApiError(message, status, data, errorCode);
  } else if (error.request) {
    // 请求已发送，但没有收到响应
    return new ApiError("无法连接到服务器，请检查网络连接", 0);
  } else {
    // 设置请求时发生了错误
    // 不直接使用error.message，因为它可能包含敏感信息
    return new ApiError("请求配置错误", 0);
  }
}
