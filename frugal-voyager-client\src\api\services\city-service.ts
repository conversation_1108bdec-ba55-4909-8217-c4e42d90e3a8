/**
 * 城市相关API服务
 */
import { api } from "../instance";
import type { City, CityRecommendRequest } from "../types/city";
import { handleErrorWithNotification } from "../utils/errorNotification";

/**
 * 城市API服务
 */
export const cityService = {
  /**
   * 获取推荐城市列表
   * @param request - 城市推荐请求参数
   * @returns 推荐城市列表
   */
  async getRecommendedCities(request: CityRecommendRequest): Promise<City[]> {
    try {
      const response = await api.post<City[]>("/cities/recommend", request);
      return response.data;
    } catch (error) {
      // 使用统一的错误处理
      handleErrorWithNotification(error, {
        customMessage: "获取推荐城市失败，请稍后再试",
      });
      // 返回空数组，避免UI出错
      return [];
    }
  },
};
