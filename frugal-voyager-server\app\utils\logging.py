import sys
import os
from pathlib import Path
from loguru import logger

from app.config import settings


def setup_logging():
    """
    为应用程序配置日志记录。
    """
    # 如果日志目录不存在则创建
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # 移除默认处理器
    logger.remove()

    # 添加控制台处理器
    logger.add(
        sys.stdout,
        colorize=True,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=settings.LOG_LEVEL,
    )

    # 添加文件处理器
    logger.add(
        "logs/app.log",
        rotation="10 MB",
        retention="1 week",
        level=settings.LOG_LEVEL,
    )

    # 添加错误文件处理器
    logger.add(
        "logs/error.log",
        rotation="10 MB",
        retention="1 month",
        level="ERROR",
    )

    logger.info(f"日志初始化完成。环境: {settings.ENV}")

    return logger
