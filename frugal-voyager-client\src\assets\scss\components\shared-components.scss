// Shared component styles
// This file contains common styles used across multiple components

// Content page base styles
.content-page {
  display: block;
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
  
  &.inactive {
    display: none;
    opacity: 0;
    transform: translateX(20px);
  }
  
  &.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
  }
}

// Common list container
.item-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// Common list item
.list-item {
  display: flex;
  background: var(--gradient-card);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  }
}

// Common item content
.item-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

// Common item title
.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 8px;
}

// Common item description
.item-description {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 12px;
}

// Common item metadata
.item-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #718096;

  span {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

// Common selector container (for date/day selectors)
.selector-container {
  width: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0;
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  height: 410px;
  position: relative;
}

// Common selector header
.selector-header {
  width: 100%;
  padding: 15px 0 10px 0;
  background: rgba(255, 255, 255, 0.3);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center;
}

// Common selector content
.selector-content {
  padding: 10px;
  overflow-y: scroll;
  max-height: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// Common selector item
.selector-item {
  width: 60px;
  min-height: 60px;
  background: var(--gradient-card);
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &.selected {
    background: var(--gradient-selected);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    
    .selector-primary,
    .selector-secondary {
      color: white;
    }
  }
}

// Common selector primary text
.selector-primary {
  font-size: 18px;
  font-weight: 700;
  color: #1a365d;
}

// Common selector secondary text
.selector-secondary {
  font-size: 12px;
  color: #4a5568;
  margin-top: 4px;
}

// Common container layout for split views
.split-container {
  display: flex;
  height: 410px;
  position: relative;
  overflow: hidden;
}

// Common content container
.content-container {
  flex: 1;
  display: flex;
  max-width: calc(100% - 100px);
}
