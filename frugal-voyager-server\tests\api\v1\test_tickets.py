"""
车票API测试。
"""

import datetime
import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from app.schemas.ticket import TicketRequest, TicketResponse, Ticket, DateInfo, SeatInfo

# 获取当天日期转换为 YYYY-MM-DD 格式
today = datetime.datetime.now().strftime("%Y-%m-%d")


@pytest.mark.parametrize(
    "city_name, departure_date, return_date, expected_status_code",
    [
        ("上海", today, None, 200),  # 只有去程
        ("上海", today, today, 200),  # 去程和返程
        ("", today, None, 400),  # 空城市名应该返回400（验证错误）
        ("上海", "", None, 400),  # 空日期应该返回400（验证错误）
    ],
)
def test_search_tickets(
    client: TestClient, city_name, departure_date, return_date, expected_status_code
):
    """
    测试车票搜索API。
    """
    # 模拟TicketService.get_available_tickets方法
    with patch(
        "app.api.v1.endpoints.tickets.TicketService.get_available_tickets"
    ) as mock_get:
        # 设置模拟返回值
        mock_tickets = TicketResponse(
            departureTickets=[
                Ticket(
                    id="G105",
                    departureTime="08:30",
                    departureStation="杭州东",
                    arrivalTime="10:30",
                    arrivalStation="上海虹桥",
                    duration="2小时",
                    price="¥100",
                    trainNumber="G105",
                    tags=[
                        SeatInfo(name="二等座", count=10, price=100),
                        SeatInfo(name="一等座", count=5, price=180),
                    ],
                )
            ],
            returnTickets=[],
        )
        mock_get.return_value = mock_tickets

        # 准备请求数据
        request_data = {
            "city_name": city_name,
            "departure_date": departure_date,
        }

        # 如果有返程日期，添加到请求中
        if return_date:
            request_data["return_date"] = return_date

            # 如果有返程日期，模拟返程车票
            mock_tickets.return_tickets = [
                Ticket(
                    id="G106",
                    departureTime="16:30",
                    departureStation="上海虹桥",
                    arrivalTime="18:30",
                    arrivalStation="杭州东",
                    duration="2小时",
                    price="¥100",
                    trainNumber="G106",
                    tags=[
                        SeatInfo(name="二等座", count=8, price=100),
                        SeatInfo(name="一等座", count=3, price=180),
                    ],
                )
            ]

        # 发送请求
        response = client.post(
            "/api/v1/tickets/search",
            json=request_data,
        )

        # 验证状态码
        assert response.status_code == expected_status_code

        # 如果请求成功，验证返回的数据
        if expected_status_code == 200:
            data = response.json()
            assert "departureTickets" in data
            assert "returnTickets" in data
            assert isinstance(data["departureTickets"], list)
            assert isinstance(data["returnTickets"], list)

            # 验证模拟函数被调用
            mock_get.assert_called_once()
            request = mock_get.call_args[0][0]
            assert isinstance(request, TicketRequest)
            assert request.city_name == city_name
            assert request.departure_date == departure_date

            # 验证返程日期
            if return_date:
                assert request.return_date == return_date
                assert len(data["returnTickets"]) > 0
