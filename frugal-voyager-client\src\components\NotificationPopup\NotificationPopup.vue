<script setup lang="ts">
import { ref, watch, computed, onUnmounted } from "vue";

// 定义通知类型
type NotificationType = "info" | "success" | "warning" | "error";

// 定义通知位置
type NotificationPosition = "top" | "center" | "bottom";

// 定义组件属性
const props = defineProps<{
  // 是否显示弹窗
  show: boolean;
  // 消息类型
  type?: NotificationType;
  // 消息标题
  title?: string;
  // 消息内容
  message: string;
  // 自动关闭时间(毫秒)，0表示不自动关闭
  duration?: number;
  // 弹窗位置
  position?: NotificationPosition;
}>();

// 定义事件
const emit = defineEmits<{
  // 关闭事件
  (e: "close"): void;
}>();

// 内部状态
const visible = ref(false);
const timer = ref<number | null>(null);

// 弹窗类型对应的样式
const typeClasses: Record<NotificationType, string> = {
  info: "notification-info",
  success: "notification-success",
  warning: "notification-warning",
  error: "notification-error",
};

// 弹窗位置对应的样式
const positionClasses: Record<NotificationPosition, string> = {
  top: "notification-top",
  center: "notification-center",
  bottom: "notification-bottom",
};

// 获取类型样式
const typeClass = computed(() => {
  return typeClasses[props.type || "info"];
});

// 获取位置样式
const positionClass = computed(() => {
  return positionClasses[props.position || "center"];
});

// 关闭弹窗
const closeNotification = () => {
  visible.value = false;
  // 延迟发送关闭事件，等待动画完成
  setTimeout(() => {
    emit("close");
  }, 300);
};

// 清除定时器
const clearTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value);
    timer.value = null;
  }
};

// 监听show属性变化
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      visible.value = true;
      // 如果设置了自动关闭时间，则启动定时器
      if (props.duration && props.duration > 0) {
        clearTimer();
        timer.value = window.setTimeout(() => {
          closeNotification();
        }, props.duration);
      }
    } else {
      visible.value = false;
    }
  },
  { immediate: true }
);

// 鼠标悬停时暂停自动关闭
const onMouseEnter = () => {
  clearTimer();
};

// 鼠标离开时恢复自动关闭
const onMouseLeave = () => {
  if (props.duration && props.duration > 0 && visible.value) {
    timer.value = window.setTimeout(() => {
      closeNotification();
    }, props.duration);
  }
};

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimer();
});
</script>

<template>
  <Transition name="notification-fade">
    <div
      v-if="visible"
      :class="['notification-popup', typeClass, positionClass]"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLeave"
    >
      <div class="notification-content">
        <div v-if="title" class="notification-title">{{ title }}</div>
        <div class="notification-message">{{ message }}</div>
      </div>
      <button class="notification-close" @click="closeNotification">×</button>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
.notification-popup {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  max-width: 450px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;

  // 默认位置居中
  left: 50%;
  transform: translateX(-50%);

  // 添加微妙的内部阴影效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.05) 100%
    );
    border-radius: 12px;
    pointer-events: none;
  }
}

// 位置样式
.notification-top {
  top: 20px; // 与价格按钮触发的提示框位置一致
  max-width: 450px; // 限制最大宽度
  left: 50%; // 水平居中
  transform: translateX(-50%); // 水平居中
}

.notification-center {
  top: 50%;
  transform: translate(-50%, -50%);
}

.notification-bottom {
  bottom: 20px;
}

// 类型样式 - 调整为与选中输入框相似的背景色，通过渐变色区分类型
// 信息提示 - 使用默认蓝色渐变
.notification-info {
  background: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.85) 0%,
    rgba(26, 54, 93, 0.75) 100%
  );
  color: white;
  border-left: 4px solid rgba(144, 156, 170, 0.8);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(144, 156, 170, 0.15) 0%,
      rgba(144, 156, 170, 0.05) 100%
    );
    border-radius: 12px;
    pointer-events: none;
  }
}

// 成功提示 - 使用蓝色渐变
.notification-success {
  background: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.85) 0%,
    rgba(26, 54, 93, 0.75) 100%
  );
  color: white;
  border-left: 4px solid rgba(144, 205, 244, 0.8);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(79, 209, 197, 0.15) 0%,
      rgba(79, 209, 197, 0.05) 100%
    );
    border-radius: 12px;
    pointer-events: none;
  }
}

// 警告提示 - 使用橙色渐变
.notification-warning {
  background: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.85) 0%,
    rgba(26, 54, 93, 0.75) 100%
  );
  color: white;
  border-left: 4px solid rgba(240, 198, 184, 0.8);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(226, 159, 132, 0.15) 0%,
      rgba(226, 159, 132, 0.05) 100%
    );
    border-radius: 12px;
    pointer-events: none;
  }
}

// 错误提示 - 使用红色渐变
.notification-error {
  background: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.85) 0%,
    rgba(26, 54, 93, 0.75) 100%
  );
  color: white;
  border-left: 4px solid rgba(226, 132, 132, 0.8);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(229, 62, 62, 0.15) 0%,
      rgba(229, 62, 62, 0.05) 100%
    );
    border-radius: 12px;
    pointer-events: none;
  }
}

.notification-content {
  flex: 1;
  padding-right: 10px;
}

.notification-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 6px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.notification-message {
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.95;
  letter-spacing: 0.3px;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 20px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  opacity: 0.7;
  transition: opacity 0.2s;

  &:hover {
    opacity: 1;
  }
}

// 动画
.notification-fade-enter-active,
.notification-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.notification-fade-enter-from,
.notification-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

.notification-fade-enter-to,
.notification-fade-leave-from {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

// 顶部位置的特殊动画
.notification-top {
  &.notification-fade-enter-from,
  &.notification-fade-leave-to {
    transform: translateX(-50%) translateY(-10px);
  }

  &.notification-fade-enter-to,
  &.notification-fade-leave-from {
    transform: translateX(-50%) translateY(0);
  }
}

// 底部位置的特殊动画
.notification-bottom {
  &.notification-fade-enter-from,
  &.notification-fade-leave-to {
    transform: translateX(-50%) translateY(10px);
  }

  &.notification-fade-enter-to,
  &.notification-fade-leave-from {
    transform: translateX(-50%) translateY(0);
  }
}
</style>
