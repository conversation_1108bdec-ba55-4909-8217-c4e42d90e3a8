import { ref, reactive } from "vue";
import { useItemSelection } from "@/composables/useItemSelection";
import { useScrollContainer } from "@/composables/useScrollContainer";
import { cityService } from "@/api/services/city-service";
import type { City } from "@/api/types/city";

/**
 * 目的地数据接口
 * 与API中的City接口兼容
 */
export interface Destination {
  id: string;
  name: string;
  description: string;
  rating: string;
  cost: string;
  recommendedStay: string;
  imageUrl: string;
  events?: Array<{
    name: string;
    time: string;
    price: string;
  }>;
}

/**
 * 将API的City类型转换为Destination类型
 * @param city API返回的城市数据
 * @returns 转换后的目的地数据
 */
export function cityToDestination(city: City): Destination {
  return {
    id: city.id,
    name: city.name,
    description: city.description,
    rating: city.rating,
    cost: city.cost,
    recommendedStay: city.recommendedStay,
    imageUrl: city.imageUrl,
    events: city.events,
  };
}

/**
 * 目的地数据
 * 将来可以从API获取
 */
export const destinations: Destination[] = [
  {
    id: "tokyo",
    name: "日本东京",
    description:
      "东京是一座充满活力的城市，融合了传统文化与现代科技。这里有美食、购物、历史景点和先进科技，适合各类旅行者。",
    rating: "4.8",
    cost: "中等",
    recommendedStay: "5-7 天",
    imageUrl:
      "https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
  },
  {
    id: "chiangmai",
    name: "泰国清迈",
    description:
      "清迈是泰国北部的文化中心，以其古老的寺庙、传统手工艺和美食而闻名。这里的生活成本较低，是背包客和数字游民的热门目的地。",
    rating: "4.6",
    cost: "低",
    recommendedStay: "3-5 天",
    imageUrl:
      "https://images.unsplash.com/photo-1528181304800-259b08848526?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
  },
  {
    id: "danang",
    name: "越南岘港",
    description:
      "岘港拥有美丽的海滩、独特的建筑和丰富的文化遗产。这里的物价较为亲民，是越南中部的旅游热点，适合寻求海滩度假的旅行者。",
    rating: "4.5",
    cost: "低",
    recommendedStay: "3-4 天",
    imageUrl:
      "https://images.unsplash.com/photo-1559592413-7cec4d0cae2b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
  },
  {
    id: "penang",
    name: "马来西亚槟城",
    description:
      "槟城以其殖民地建筑、街头艺术和美食而著名。这座城市融合了马来、中国和印度文化，是一个多元文化的熔炉，特别适合美食爱好者。",
    rating: "4.7",
    cost: "低至中等",
    recommendedStay: "2-4 天",
    imageUrl:
      "https://images.unsplash.com/photo-1567157577867-05ccb1388e66?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
  },
];

/**
 * 目的地选择逻辑
 * 提供目的地选择相关的功能
 *
 * @param options 配置选项
 * @returns 目的地选择相关的状态和方法
 */
export function useDestinationSelection(
  options: {
    onSelect?: (destination: string) => void;
    useApi?: boolean;
    position?: string;
  } = {}
) {
  const { onSelect, useApi = false, position = "杭州" } = options;

  // 加载状态
  const isLoading = ref(false);
  // 目的地列表
  const destinationList = reactive<Destination[]>([...destinations]);

  // 使用通用的选择逻辑
  const selectionManager = useItemSelection({
    onSelect: (selected) => {
      if (onSelect && typeof selected === "string") {
        onSelect(selected);
      }
    },
  });

  // 滚动容器管理
  const scrollManager = useScrollContainer({
    hideScrollbar: false,
  });

  /**
   * 从API获取推荐城市
   * @param customPosition 自定义出发城市，如果提供则覆盖初始化时的position
   */
  const fetchDestinations = async (customPosition?: string) => {
    if (!useApi) return;

    // 使用自定义出发城市或初始化时的position
    const currentPosition = customPosition || position;
    console.log(`获取推荐城市，出发城市: ${currentPosition}`);

    isLoading.value = true;
    try {
      const cities = await cityService.getRecommendedCities({
        position: currentPosition,
      });
      // 清空当前列表并添加新数据
      destinationList.splice(0, destinationList.length);
      cities.map(cityToDestination).forEach((dest) => {
        destinationList.push(dest);
      });
    } catch (error) {
      console.error("获取推荐城市失败:", error);
      // 发生错误时，如果列表为空，则使用本地数据
      if (destinationList.length === 0) {
        destinations.forEach((dest) => {
          destinationList.push(dest);
        });
      }
    } finally {
      isLoading.value = false;
    }
  };

  // 不再在组件挂载时自动获取数据
  // 而是由父组件通过调用fetchDestinations方法来控制数据加载

  /**
   * 选择目的地
   * @param destination 目的地名称
   * @returns 选择的目的地
   */
  const selectDestination = (destination: string) => {
    return selectionManager.selectItem(destination) as string;
  };

  /**
   * 获取目的地详情
   * @param destinationName 目的地名称
   * @returns 目的地详情
   */
  const getDestinationDetails = (
    destinationName: string
  ): Destination | undefined => {
    return destinationList.find((dest) => dest.name === destinationName);
  };

  return {
    selectedDestination: selectionManager.selectedItems,
    destinationList,
    isLoading,
    selectDestination,
    getDestinationDetails,
    fetchDestinations,
    ...scrollManager,
  };
}

/**
 * 获取目的地数据
 * 支持从API获取数据
 */
export function useDestinationData() {
  // 加载状态
  const isLoading = ref(false);
  // 目的地数据
  const apiDestinations = ref<Destination[]>([]);

  /**
   * 从API获取推荐城市
   * @param position 出发城市
   */
  const fetchRecommendedCities = async (position: string = "杭州") => {
    isLoading.value = true;
    try {
      const cities = await cityService.getRecommendedCities({ position });
      // 将API返回的城市数据转换为目的地数据
      apiDestinations.value = cities.map(cityToDestination);
      return apiDestinations.value;
    } catch (error) {
      console.error("获取推荐城市失败:", error);
      // 发生错误时使用本地数据
      apiDestinations.value = [...destinations];
      return apiDestinations.value;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 获取所有目的地
   * @param useApi 是否使用API获取数据，默认为false
   * @param position 出发城市，默认为"杭州"
   * @returns 所有目的地数据
   */
  const getAllDestinations = async (
    useApi: boolean = false,
    position: string = "杭州"
  ) => {
    if (useApi) {
      return await fetchRecommendedCities(position);
    }
    // 如果已经从API获取过数据，则使用缓存的数据
    if (apiDestinations.value.length > 0) {
      return apiDestinations.value;
    }
    // 否则使用本地数据
    return destinations;
  };

  /**
   * 按条件筛选目的地
   * @param criteria 筛选条件
   * @returns 筛选后的目的地
   */
  const filterDestinations = (criteria: Partial<Destination>) => {
    // 优先使用API数据，如果没有则使用本地数据
    const dataSource =
      apiDestinations.value.length > 0 ? apiDestinations.value : destinations;

    return dataSource.filter((dest) => {
      // 根据提供的条件进行筛选
      return Object.entries(criteria).every(([key, value]) => {
        const destValue = dest[key as keyof Destination];
        // 添加空值检查
        if (destValue === undefined || destValue === null) {
          return false;
        }
        return destValue.toString().includes(value.toString());
      });
    });
  };

  return {
    isLoading,
    apiDestinations,
    getAllDestinations,
    filterDestinations,
    fetchRecommendedCities,
  };
}
