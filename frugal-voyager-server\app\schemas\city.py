from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict


class Event(BaseModel):
    """活动模型"""

    name: str
    time: str
    price: str


class CityBase(BaseModel):
    """具有共同属性的基础城市模型。"""

    id: str
    name: str
    description: str
    rating: str
    cost: str
    recommended_stay: str = Field(..., alias="recommendedStay")
    image_url: str = Field(..., alias="imageUrl")
    events: Optional[List[Event]] = None


class City(CityBase):
    """用于响应的城市模型。"""

    model_config = ConfigDict(
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "id": "tokyo",
                "name": "日本东京",
                "description": "东京是一座充满活力的城市，融合了传统文化与现代科技。这里有美食、购物、历史景点和先进科技，适合各类旅行者。",
                "rating": "4.8",
                "cost": "￥500/天",
                "recommendedStay": "5-7 天",
                "imageUrl": "https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
                "events": [
                    {"name": "东京樱花节", "time": "03/25 ~ 04/10", "price": "0"},
                    {"name": "浅草寺新年庆典", "time": "01/01 ~ 01/03", "price": "0"},
                ],
            }
        },
    )


class CityRequest(BaseModel):
    """用于生成城市推荐的请求模型。"""

    position: str = Field(..., description="出发城市", min_length=1)

    model_config = ConfigDict(json_schema_extra={"example": {"position": "杭州"}})
