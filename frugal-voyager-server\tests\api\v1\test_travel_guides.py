"""
旅游指南API测试。
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from app.schemas.mcp import GenerateRequest
from app.schemas.travel_guide import GuideItem, Attraction, Transportation


@pytest.mark.parametrize(
    "city, days, expected_status_code",
    [
        ("杭州", 2, 200),
        ("", 2, 400),  # 空城市名应该返回400（验证错误）
        ("杭州", 0, 400),  # 0天应该返回400（验证错误），因为days必须>=1
    ],
)
def test_generate_travel_guide(client: TestClient, city, days, expected_status_code):
    """
    测试生成旅游指南API。
    """
    # 模拟MCPService.generate_travel_guide_items方法
    with patch(
        "app.api.v1.endpoints.travel_guides.MCPService.generate_travel_guide_items"
    ) as mock_generate:
        # 设置模拟返回值
        mock_guide_items = [
            Attraction(
                id="hangzhou-attraction-1",
                type="attraction",
                time="09:00 - 11:00",
                name="西湖",
                description="西湖是杭州市区西部的淡水湖，是中国大陆首批国家重点风景名胜区和中国十大风景名胜之一。",
                ticketPrice="免费",
                recommendedStay="2小时",
                imageUrl="https://example.com/xihu.jpg",
                position="left",
            ),
            Transportation(
                id="hangzhou-transport-1",
                type="transportation",
                time="11:00 - 11:30",
                icon="🚌",
                duration="30分钟",
                detail="从西湖到灵隐寺",
            ),
        ]
        mock_generate.return_value = mock_guide_items

        # 发送请求
        response = client.post(
            "/api/v1/travel-guides/generate-guide",
            json={"city": city, "days": days},
        )

        # 验证状态码
        assert response.status_code == expected_status_code

        # 如果请求成功，验证返回的数据
        if expected_status_code == 200:
            data = response.json()
            assert isinstance(data, list)
            if len(data) > 0:
                assert "id" in data[0]
                assert "type" in data[0]
                assert "time" in data[0]

            # 验证模拟函数被调用
            mock_generate.assert_called_once()
            assert mock_generate.call_args[0][0] == city
            # 验证传入的天数
            assert mock_generate.call_args[0][1] == days
