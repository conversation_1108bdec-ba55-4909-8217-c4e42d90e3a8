import requests
import json
import datetime
from pypinyin import pinyin, lazy_pinyin, Style


base_url = "https://kyfw.12306.cn"
public_name = "/otn"


def get_ticket_list(
    train_no: str,
    from_station_telecode: str,
    to_station_telecode: str,
    depart_date: str,
):
    """
    获取车次经停信息
    :param train_no: 列车号
    :param from_station_telecode: 出发站编号
    :param to_station_telecode: 到达站编号
    :param depart_date: 出发日期
    :return: 车次经停信息
    """

    # 查询车次信息
    left_ticket_list_url = "/czxx"

    session = requests.Session()
    session.headers = {
        "User-Agent": "Mozilla/5.0...",
        "Host": "kyfw.12306.cn",
        "Referer": f"{base_url}{public_name}{left_ticket_list_url}/init",
    }

    # 获取初始化cookies
    session.get(f"{base_url}{public_name}{left_ticket_list_url}/init")

    # 构造查询参数
    params = {
        "train_no": train_no,
        "from_station_telecode": from_station_telecode,
        "to_station_telecode": to_station_telecode,
        "depart_date": depart_date,
    }

    response = session.get(
        f"{base_url}{public_name}{left_ticket_list_url}/queryByTrainNo", params=params
    )

    # 判断是否获取成功
    if response.status_code != 200 or response.text == "":
        return []

    return response.json()["data"]["data"].json()


def get_ticket_price(
    train_no: str,
    from_station_no: str,
    to_station_no: str,
    seat_types: str,
    train_date: str,
):
    """
    获取车票价格
    :param train_no: 列车号
    :param from_station_no: 出发站编号
    :param to_station_no: 到达站编号
    :param seat_types: 座位类型
    :param train_date: 出发日期
    :return: 车票价格
    """

    # 查询车票信息
    left_ticket_url = "/leftTicket"

    session = requests.Session()
    session.headers = {
        "User-Agent": "Mozilla/5.0...",
        "Host": "kyfw.12306.cn",
        "Referer": f"{base_url}{public_name}{left_ticket_url}/init",
    }

    # 获取初始化cookies
    session.get(f"{base_url}{public_name}{left_ticket_url}/init")

    # 构造查询参数
    params = {
        "train_no": train_no,
        "from_station_no": from_station_no,
        "to_station_no": to_station_no,
        "seat_types": seat_types,
        "train_date": train_date,
    }

    response = session.get(
        f"{base_url}{public_name}{left_ticket_url}/queryTicketPrice", params=params
    )

    # 判断是否获取成功
    if response.status_code != 200 or response.text == "":
        return 0

    # 解析车票价格
    price_info = response.json()["data"]

    return price_info.json()


def get_train_all_city_from_net():
    """
    获取所有城市信息
    :return: 所有城市信息
    """

    # 获取所有城市信息
    city_url = "/resources/js/framework"

    session = requests.Session()
    session.headers = {
        "User-Agent": "Mozilla/5.0...",
        "Host": "kyfw.12306.cn",
        "Referer": f"{base_url}{public_name}{city_url}/init",
    }

    # 获取初始化cookies
    session.get(f"{base_url}{public_name}{city_url}/init")
    response = session.get(
        f"{base_url}{public_name}{city_url}/favorite_name.js",
    )

    # 判断是否获取成功
    if response.status_code != 200 or response.text == "":
        return []

    # 根据逗号进行字符串分割
    js_str = response.text
    js_str = js_str.split("'")
    js_str = js_str[1]
    js_str = js_str.split("@")

    # 获取城市信息
    city_info = []
    for item in js_str:
        if item == "":
            continue
        item = item.split("|")
        city_info.append(
            {
                "name": item[1],
                "station_code": item[2],
                "ping_yin": "".join(lazy_pinyin(item[1])),
                "ping_yin_short": item[0],
            }
        )

    # 将城市信息写入文件, 编码格式为utf-8
    with open("types/train_all_station.json", "w", encoding="utf-8") as f:
        json.dump(city_info, f, ensure_ascii=False)


def get_tickets(fromDate: str, fromStation: str, toStation: str, purposeCodes: str):
    """
    获取车票信息
    :param fromDate: 出发日期
    :param fromStation: 出发站
    :param toStation: 到达站
    :param purposeCodes: 乘客类型
    :return: 车票信息
    """

    # 查询车票信息
    left_ticket_url = "/leftTicket"

    session = requests.Session()
    session.headers = {
        "User-Agent": "Mozilla/5.0...",
        "Host": "kyfw.12306.cn",
        "Referer": f"{base_url}{public_name}{left_ticket_url}/init",
    }

    # 获取初始化cookies
    session.get(f"{base_url}{public_name}{left_ticket_url}/init")

    # 构造查询参数
    params = {
        "leftTicketDTO.train_date": fromDate,
        "leftTicketDTO.from_station": fromStation,
        "leftTicketDTO.to_station": toStation,
        "purpose_codes": purposeCodes,
    }

    response = session.get(
        f"{base_url}{public_name}{left_ticket_url}/query", params=params
    )

    # 判断是否获取成功
    if response.status_code != 200 or response.text == "":
        return []

    # 解析车次信息
    train_list = response.json()["data"]["result"]
    train_info = []
    for train_item in train_list:
        train_item_info = analysisOfTrainNumberInfor(train_item)
        train_info.append(train_item_info)

    return train_info


# 解析车次信息
def analysisOfTrainNumberInfor(info: str):
    """
    解析车次信息
    :param info: 车次信息
    :return
    """

    data_list = info.split("|")

    data_info = {
        "secret_str": data_list[0],  # 无用
        "button_text_info": data_list[1],  # 按钮名字：预定
        "train_no": data_list[2],  # 列车号
        "train_code": data_list[3],  # 车次
        "start_station_code": data_list[4],  # 起始站编号
        "end_station_code": data_list[5],  # 终点站编号
        "from_station_code": data_list[6],  # 出发站编号
        "to_station_code": data_list[7],  # 到达站编号
        "start_time": data_list[8],  # 出发时间
        "arrive_time": data_list[9],  # 到达时间
        "total_time": data_list[10],  # 总耗时
        "can_web_buy": data_list[
            11
        ],  # 是否可购买：Y 可购买 N 不可购买 IS_TIME_NOT_BUY 时间未定
        "yp_info": data_list[12],  # 无用
        "start_train_date": data_list[13],  # 起始站出发日期
        "train_seat_feature": data_list[14],  # 无用
        "location_code": data_list[15],  # 无用
        "from_station_no": data_list[
            16
        ],  # 出发站站序（对应火车经停信息中的站序）01表示始发站，大于1则表示过站
        "to_station_no": data_list[17],  # 到达站站序（对应火车经停信息中的站序）
        "is_support_card": data_list[18],  # 是否支持二代身份证进站：1 支持 0 不支持
        "controlled_train_flag": data_list[19],  # 无用
        "gg_num": data_list[20],  # ？
        "gr_num": data_list[21],  # 高级软卧
        "qt_num": data_list[22],  # 其他
        "rw_num": data_list[23],  # 软卧，一等卧
        "rz_num": data_list[24],  # 软座
        "tz_num": data_list[25],  # 特等座
        "wz_num": data_list[26],  # 无座
        "yb_num": data_list[27],  # ？
        "yw_num": data_list[28],  # 硬卧，二等卧
        "yz_num": data_list[29],  # 硬座
        "edz_num": data_list[30],  # 二等座
        "ydz_num": data_list[31],  # 一等座
        "swz_num": data_list[32],  # 商务座
        "srrb_num": data_list[33],  # 动卧
        "yp_ex": data_list[34],  # 查询车票价格时的 seat_types 字段
        "seat_types": data_list[35],  # 座位类型
        "exchange_train_flag": data_list[36],  # 无用
    }

    return data_info


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) >= 4:
        fromDate = sys.argv[1]
        fromStation = sys.argv[2]
        toStation = sys.argv[3]
        purposeCodes = sys.argv[4] if len(sys.argv) > 4 else "ADULT"
    else:
        fromDate = datetime.datetime.now().strftime("%Y-%m-%d")  # 默认今天
        fromStation = "HZH"  # 默认杭州
        toStation = "SHH"  # 默认上海
        purposeCodes = "ADULT"  # 默认成人票

    # 获取车次信息
    result = get_tickets(fromDate, fromStation, toStation, purposeCodes)

    # 输出 JSON 格式的结果
    print(json.dumps(result, ensure_ascii=False))
