import { ref } from "vue";

export interface StepState {
  active: boolean;
  inProgress: boolean;
}

/**
 * 步骤管理器
 * 用于管理多步骤流程中的步骤状态和切换
 *
 * @param initialStep 初始步骤索引
 * @param totalSteps 总步骤数
 * @returns 步骤管理相关的状态和方法
 */
export function useStepManager(initialStep = 0, totalSteps = 3) {
  // 当前步骤
  const currentStep = ref(initialStep);

  // 步骤状态数组
  const stepStates = ref<StepState[]>(
    Array(totalSteps)
      .fill(0)
      .map((_, i) => ({
        active: i === initialStep,
        inProgress: i === initialStep,
      }))
  );

  /**
   * 切换到指定步骤
   * @param index 步骤索引
   */
  const switchStep = (index: number) => {
    if (index < 0 || index >= totalSteps) {
      console.warn(`步骤索引 ${index} 超出范围 [0, ${totalSteps - 1}]`);
      return;
    }

    // 更新当前步骤
    currentStep.value = index;

    // 更新步骤状态
    stepStates.value = stepStates.value.map((_, i) => {
      if (i === index) {
        // 当前步骤为激活状态，是进行中
        return { active: true, inProgress: true };
      } else if (i < index) {
        // 之前的步骤为激活状态，不是进行中
        return { active: true, inProgress: false };
      } else {
        // 其他步骤既不是激活也不是进行中
        return { active: false, inProgress: false };
      }
    });
  };

  /**
   * 前进到下一步
   */
  const nextStep = () => {
    if (currentStep.value < totalSteps - 1) {
      switchStep(currentStep.value + 1);
    }
  };

  /**
   * 返回上一步
   */
  const prevStep = () => {
    if (currentStep.value > 0) {
      switchStep(currentStep.value - 1);
    }
  };

  /**
   * 重置步骤到初始状态
   */
  const resetSteps = () => {
    switchStep(initialStep);
  };

  return {
    currentStep,
    stepStates,
    switchStep,
    nextStep,
    prevStep,
    resetSteps,
  };
}
