from typing import List
from fastapi import APIRouter, Request
from loguru import logger

from app.schemas.mcp import GenerateRequest, GenerateResponse
from app.schemas.travel_guide import GuideItem
from app.services.mcp_service import MCPService
from app.utils.exceptions import ServiceException
from app.schemas.errors import ErrorCode
from app.config import settings

router = APIRouter()


@router.post("/generate-guide", response_model=List[GuideItem], summary="生成旅游指南")
async def generate_travel_guide(request: GenerateRequest, req: Request):
    """
    使用MCP服务生成旅游指南（新版本，返回结构化数据）。

    - **city**: 城市名称，例如"杭州"
    - **days**: 旅游天数，默认为2天
    - **train_info**: 火车票信息，包含出发和返程时间（可选）

    返回生成的旅游指南（结构化数据，包含景点和交通信息）。
    """
    client_host = req.client.host if req.client else "unknown"
    logger.info(
        f"收到生成旅游指南请求: 城市={request.city}, 天数={request.days}, 客户端IP={client_host}"
    )

    if request.train_info:
        logger.info(
            f"请求包含火车票信息: 去程={request.train_info.departure_train}, 返程={request.train_info.return_train}"
        )

    try:
        # 调用MCP服务生成旅游指南项目
        guide_items = await MCPService.generate_travel_guide_items(
            request.city, request.days, request.train_info
        )

        if not guide_items:
            logger.warning(f"未能生成任何旅游指南项目")
            raise ServiceException(
                detail="未能生成旅游指南，请稍后重试",
                error_code=ErrorCode.MCP_SERVICE_ERROR,
            )

        return guide_items
    except ServiceException:
        # 直接重新抛出已经是ServiceException的异常
        raise
    except Exception as e:
        error_msg = f"生成旅游指南时出错: {str(e)}"
        logger.error(error_msg)
        raise ServiceException(
            detail=f"生成旅游指南失败: {str(e) if settings.ENV == 'development' else '请稍后重试'}",
            error_code=ErrorCode.MCP_SERVICE_ERROR,
        )
