"""
字符串处理工具函数。

提供处理字符串的通用函数。
"""
import re
from typing import Optional
from loguru import logger


def remove_extra_whitespace(text: str) -> str:
    """
    移除多余的空白字符。
    
    Args:
        text: 原始文本
        
    Returns:
        处理后的文本
    """
    # 移除多余的空白字符
    return re.sub(r'\s+', ' ', text).strip()


def extract_between_markers(text: str, start_marker: str, end_marker: str) -> Optional[str]:
    """
    从文本中提取两个标记之间的内容。
    
    Args:
        text: 原始文本
        start_marker: 开始标记
        end_marker: 结束标记
        
    Returns:
        提取的内容，如果没有找到则返回None
    """
    try:
        if start_marker in text and end_marker in text:
            start_index = text.index(start_marker) + len(start_marker)
            end_index = text.index(end_marker, start_index)
            return text[start_index:end_index].strip()
        return None
    except ValueError:
        logger.warning(f"无法在文本中找到标记: {start_marker} 或 {end_marker}")
        return None


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断文本到指定长度。
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 截断后添加的后缀
        
    Returns:
        截断后的文本
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix
