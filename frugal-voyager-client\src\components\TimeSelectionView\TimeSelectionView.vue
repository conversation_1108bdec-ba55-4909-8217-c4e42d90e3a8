<script setup lang="ts">
import {
  useDateSelection,
  useTrainSelection,
  useTimeSelectionInit,
  dateItems,
  outboundTrains,
  returnTrains,
  selectedOutboundTrain,
  selectedReturnTrain,
  simplifyName,
  isLoading,
} from "./TimeSelectionView";
import "./TimeSelectionView.scss";

// 组件props和emit定义
const props = defineProps<{
  // 目的地城市名称
  cityName?: string;
  // 出发城市名称
  departureCity?: string;
}>();

const emit = defineEmits<{
  (e: "datesSelected", dates: string[]): void;
}>();

// 使用组合式函数
const dateSelectionUtils = useDateSelection({
  cityName: props.cityName || "上海",
  departureCity: props.departureCity || "杭州",
});
const trainSelectionUtils = useTrainSelection({
  onSelect: (trainId, isGoList) => {
    console.log(`选择了${isGoList ? "去程" : "返程"}火车: ${trainId}`);
  },
});
const { initializeComponent } = useTimeSelectionInit(
  dateSelectionUtils,
  trainSelectionUtils
);

// 监听cityName和departureCity属性变化
import { watch, onMounted } from "vue";
watch(
  () => props.cityName,
  (newCityName) => {
    if (newCityName) {
      dateSelectionUtils.setCity(newCityName);
    }
  }
);

watch(
  () => props.departureCity,
  (newDepartureCity) => {
    if (newDepartureCity) {
      dateSelectionUtils.setDepartureCity(newDepartureCity);
    }
  }
);

// 组件挂载时记录日志，但不自动初始化
// 初始化将由父组件通过调用 initializeComponent 方法来控制
onMounted(() => {
  console.log("TimeSelectionView 组件已挂载");
});

// 解构需要的引用和方法
const { dateContainerRef, goTitleRef, returnTitleRef, handleDateClick } =
  dateSelectionUtils;
const {
  goTrainListRef,
  returnTrainListRef,
  handleTrainClick,
  goScrollPosition,
  returnScrollPosition,
} = trainSelectionUtils;

// 处理日期选择
const onDateSelect = (event: Event) => {
  // 调用日期点击处理函数，但不使用返回值
  handleDateClick(event);
  // 不再在这里触发 datesSelected 事件，而是在确认按钮点击时触发
  // 这样用户可以自由选择日期，而不会自动跳转
};

// 确认选择
const onConfirmSelection = () => {
  console.log("确认选择");
  console.log("选中的去程车票:", selectedOutboundTrain.value);
  console.log("选中的返程车票:", selectedReturnTrain.value);

  // 检查是否已选择日期
  if (dateSelectionUtils.selectedDates.value.length === 0) {
    console.warn("未选择日期，无法确认");
    return;
  }

  // 确保日期数组有效
  const dates = [...dateSelectionUtils.selectedDates.value];

  // 如果只选择了一天，则出发日期和返回日期相同
  if (dates.length === 1) {
    dates.push(dates[0]);
  } else if (dates.length > 2) {
    // 如果选择了多于两天，只取前两天
    dates.splice(2);
  }

  // 确保日期按时间顺序排序
  dates.sort();

  console.log(`确认选择日期: ${dates.join(" 至 ")}`);

  // 触发事件，将日期信息传递给父组件
  emit("datesSelected", dates);
};

// 滚动到去程列表顶部
const scrollToTopGo = () => {
  if (goTrainListRef.value) {
    goTrainListRef.value.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
};

// 滚动到返程列表顶部
const scrollToTopReturn = () => {
  if (returnTrainListRef.value) {
    returnTrainListRef.value.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
};

// 获取选中的火车票信息
const getSelectedTrains = () => {
  // 检查是否有选中的火车票
  if (!selectedOutboundTrain.value && !selectedReturnTrain.value) {
    console.log("没有选中的火车票");
    return null;
  }

  // 查找选中的去程火车票完整信息
  let outboundTrainInfo = null;
  if (selectedOutboundTrain.value) {
    outboundTrainInfo = outboundTrains.value.find(
      (train) => train.id === selectedOutboundTrain.value
    );
  }

  // 查找选中的返程火车票完整信息
  let returnTrainInfo = null;
  if (selectedReturnTrain.value) {
    returnTrainInfo = returnTrains.value.find(
      (train) => train.id === selectedReturnTrain.value
    );
  }

  // 返回选中的火车票完整信息
  return {
    outbound: outboundTrainInfo,
    return: returnTrainInfo,
  };
};

// 暴露初始化方法和获取选中火车票方法给父组件
defineExpose({
  initializeComponent,
  getSelectedTrains,
});
</script>

<template>
  <div class="content-page" id="step2-content">
    <!-- 加载状态遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner-circle"></div>
        <div class="spinner-circle-inner"></div>
      </div>
      <div class="loading-text">正在获取车票数据...</div>
    </div>

    <div class="train-container">
      <!-- 左右分栏的列表结构 -->
      <div class="train-list-container">
        <!-- 去程列表 -->
        <div class="train-list train-list-left" ref="goTrainListRef">
          <h3
            ref="goTitleRef"
            style="margin-bottom: 15px; color: #1a365d; text-align: center"
          >
            去程
          </h3>

          <!-- 无车票提示 -->
          <div v-if="outboundTrains.length === 0" class="no-tickets-message">
            <div class="no-tickets-text">未找到符合条件的车票</div>
            <div class="no-tickets-subtext">请尝试修改目的地或日期</div>
          </div>

          <!-- 去程火车票项目 -->
          <div
            v-else
            v-for="train in outboundTrains"
            :key="train.id"
            class="train-item"
            :data-train-id="train.id"
            @click="(e) => handleTrainClick(e, true)"
          >
            <div class="train-item-header">
              <div>
                <div class="train-time">{{ train.departureTime }}</div>
                <div class="train-stations">{{ train.departureStation }}</div>
              </div>
              <div>
                <div class="train-duration">{{ train.duration }}</div>
                <div
                  style="text-align: center; font-size: 18px; color: #718096"
                >
                  →
                </div>
              </div>
              <div>
                <div class="train-time">{{ train.arrivalTime }}</div>
                <div class="train-stations">{{ train.arrivalStation }}</div>
              </div>
            </div>
            <div class="train-info">
              <div class="train-number">{{ train.trainNumber }}</div>
              <div class="train-price">{{ train.price }}</div>
            </div>
            <div class="train-tags">
              <div
                v-for="(seat, index) in train.tags"
                :key="index"
                class="train-tag"
              >
                <span class="seat-name">{{ simplifyName(seat.name) }}</span>
                <span class="seat-count">{{
                  seat.count > 0 ? `${seat.count}张` : "无票"
                }}</span>
              </div>
            </div>
          </div>

          <!-- 返回顶部按钮 -->
          <div
            class="back-to-top"
            :class="{ hidden: goScrollPosition <= 50 }"
            @click="scrollToTopGo"
          >
            ↑
          </div>
        </div>

        <!-- 返程列表 -->
        <div class="train-list" ref="returnTrainListRef">
          <h3
            ref="returnTitleRef"
            style="margin-bottom: 15px; color: #1a365d; text-align: center"
          >
            返程
          </h3>

          <!-- 无车票提示 -->
          <div v-if="returnTrains.length === 0" class="no-tickets-message">
            <div class="no-tickets-text">未找到符合条件的车票</div>
            <div class="no-tickets-subtext">请尝试修改目的地或日期</div>
          </div>

          <!-- 返程火车票项目 -->
          <div
            v-else
            v-for="train in returnTrains"
            :key="train.id"
            class="train-item"
            :data-train-id="train.id"
            @click="(e) => handleTrainClick(e, false)"
          >
            <div class="train-item-header">
              <div>
                <div class="train-time">{{ train.departureTime }}</div>
                <div class="train-stations">{{ train.departureStation }}</div>
              </div>
              <div>
                <div class="train-duration">{{ train.duration }}</div>
                <div
                  style="text-align: center; font-size: 18px; color: #718096"
                >
                  →
                </div>
              </div>
              <div>
                <div class="train-time">{{ train.arrivalTime }}</div>
                <div class="train-stations">{{ train.arrivalStation }}</div>
              </div>
            </div>
            <div class="train-info">
              <div class="train-number">{{ train.trainNumber }}</div>
              <div class="train-price">{{ train.price }}</div>
            </div>
            <div class="train-tags">
              <div
                v-for="(seat, index) in train.tags"
                :key="index"
                class="train-tag"
              >
                <span class="seat-name">{{ simplifyName(seat.name) }}</span>
                <span class="seat-count">{{
                  seat.count > 0 ? `${seat.count}张` : "无票"
                }}</span>
              </div>
            </div>
          </div>

          <!-- 返回顶部按钮 -->
          <div
            class="back-to-top"
            :class="{ hidden: returnScrollPosition <= 50 }"
            @click="scrollToTopReturn"
          >
            ↑
          </div>
        </div>
      </div>

      <!-- 右侧日期选择器 -->
      <div class="train-date-selector">
        <div class="date-selector-header">
          <div class="date-title">选择日期</div>
        </div>
        <div class="date-selector-content" ref="dateContainerRef">
          <div
            v-for="dateItem in dateItems"
            :key="dateItem.date"
            class="date-item"
            :data-date="dateItem.date"
            @click="onDateSelect"
          >
            <div class="date-day">{{ dateItem.day }}</div>
            <div class="date-weekday">{{ dateItem.weekday }}</div>
          </div>
        </div>

        <!-- 确认按钮 -->
        <div class="confirm-button-container">
          <div class="confirm-button" @click="onConfirmSelection">
            <div class="date-day">确认</div>
            <div class="date-weekday">选择</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
