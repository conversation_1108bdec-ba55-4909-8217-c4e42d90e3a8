@use "@/assets/scss/utils/scrollbars" as *;
@use "@/assets/scss/components/shared-components" as *;

// Adapting component-specific styles

/* 火车票选择样式 */
.train-container {
  @extend .split-container;
}

.train-list-container {
  @extend .content-container;
}

.train-list {
  flex: 1;
  overflow-y: scroll;
  padding: 0px 5px;
  max-height: 410px;
  @include hide-scrollbar;
  isolation: isolate; /* 创建新的堆叠上下文 */
  position: relative; /* 确保定位上下文 */

  /* 确保有足够的内边距，避免内容被返回顶部按钮遮挡 */
  padding-bottom: 60px;
}

.train-list-left {
  border-right: 1px dashed rgba(26, 54, 93, 0.2);
}

/* 日期选择器样式 */
.train-date-selector {
  @extend .selector-container;
}

.date-selector-header {
  @extend .selector-header;
}

.date-selector-content {
  @extend .selector-content;
  @include hide-scrollbar;
  isolation: isolate; /* 创建新的堆叠上下文 */
  position: relative; /* 确保定位上下文 */
}

.date-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 15px;
  text-align: center;
}

/* 日期项目样式 */
.date-item {
  @extend .selector-item;

  &.range-start,
  &.range-end {
    &:after {
      content: "";
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background: #4fd1c5;
      border-radius: 3px;
    }
  }

  &.in-range {
    background: var(--gradient-range);
    transform: translateY(-1px);
  }
}

.date-day {
  @extend .selector-primary;
}

.date-weekday {
  @extend .selector-secondary;
}

// Train item styles
.train-item {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
  width: calc(100% - 10px);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    border-color: #4fd1c5;
    background: var(--gradient-card);
    box-shadow: 0 6px 20px rgba(79, 209, 197, 0.2);

    &::after {
      content: "✓";
      position: absolute;
      top: -10px;
      right: -10px;
      width: 25px;
      height: 25px;
      background: #4fd1c5;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      font-weight: bold;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
  }
}

.train-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.train-time {
  font-size: 20px;
  font-weight: 700;
  color: #1a365d;
}

.train-duration {
  font-size: 12px;
  color: #718096;
  text-align: center;
  margin-top: 4px;
}

.train-stations {
  font-size: 14px;
  color: #4a5568;
}

.train-info {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  align-items: center;
}

.train-number {
  font-size: 13px;
  color: #718096;
}

.train-price {
  font-size: 18px;
  font-weight: 700;
  color: #ed8936;
}

.train-tags {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 6px;
  margin-top: 8px;
}

.train-tag {
  font-size: 12px;
  color: #38b2ac;
  background: rgba(56, 178, 172, 0.1);
  padding: 3px 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
}

.seat-name {
  font-weight: 600;
  margin-right: 2px;
}

.seat-count {
  color: #4a5568;
  font-size: 11px;
}

/* 确认按钮容器样式 */
.confirm-button-container {
  padding: 10px;
  border-top: 1px solid rgba(26, 54, 93, 0.1);
  background: rgba(255, 255, 255, 0.3); /* 与 selector-header 背景色一致 */
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

/* 确认按钮样式 */
.confirm-button {
  @extend .selector-item;
  background: var(--gradient-selected); /* 使用与选中项相同的渐变背景 */
  margin: 0 auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

  .selector-primary,
  .selector-secondary {
    color: white; /* 确保文字颜色为白色 */
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
}

/* 无车票提示样式 */
.no-tickets-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
  background: var(--gradient-card);
  border-radius: 12px;
  margin: 20px 0;
  border: 1px solid rgba(26, 54, 93, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: calc(100% - 10px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(
      to right,
      rgba(209, 194, 189, 0.7),
      rgba(240, 198, 184, 0.7)
    );
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  }
}

.no-tickets-text {
  font-size: 16px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 10px;
  letter-spacing: 0.3px;
  position: relative;
  display: inline-block;

  &::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, #d1c2bd, #f0c6b8);
    border-radius: 1px;
  }
}

.no-tickets-subtext {
  font-size: 14px;
  color: #718096;
  letter-spacing: 0.2px;
  margin-top: 8px;
}

/* 加载状态遮罩样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.spinner-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top-color: #4fd1c5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-circle-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border: 4px solid transparent;
  border-top-color: #1a365d;
  border-radius: 50%;
  animation: spin 1.5s linear infinite reverse;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
  font-weight: 600;
  color: #1a365d;
  text-align: center;
}

/* 返回顶部按钮样式 */
.back-to-top {
  position: sticky; /* 使用 sticky 定位，在滚动时保持可见 */
  bottom: 20px;
  margin-left: auto; /* 靠右对齐 */
  margin-right: 20px; /* 右侧间距 */
  margin-bottom: 20px; /* 底部间距 */
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(79, 209, 197, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1000; /* 确保按钮在最上层 */
  font-size: 18px;

  &:hover {
    background: rgba(79, 209, 197, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
}
