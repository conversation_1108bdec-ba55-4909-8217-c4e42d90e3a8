/**
 * 错误通知工具
 *
 * 用于在UI中显示错误消息，不暴露具体的技术细节
 */
import { ApiError } from "./errorHandler";
import { showError } from "@/services/notification-service";

/**
 * 错误通知选项
 */
export interface ErrorNotificationOptions {
  /** 是否在控制台记录错误 */
  logToConsole?: boolean;
  /** 自定义错误消息 */
  customMessage?: string;
  /** 是否显示通知 */
  showNotification?: boolean;
  /** 错误标题 */
  title?: string;
  /** 通知持续时间（毫秒） */
  duration?: number;
}

/**
 * 默认错误通知选项
 */
const defaultOptions: ErrorNotificationOptions = {
  logToConsole: true,
  showNotification: true,
  title: "操作失败",
  duration: 5000,
};

/**
 * 处理API错误并显示通知
 * @param error API错误
 * @param options 错误通知选项
 */
export function handleErrorWithNotification(
  error: unknown,
  options: ErrorNotificationOptions = {}
): void {
  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options };

  // 如果不是ApiError，转换为ApiError
  const apiError =
    error instanceof ApiError
      ? error
      : new ApiError(error instanceof Error ? error.message : "未知错误", 500);

  // 获取用户友好的错误消息
  const message =
    mergedOptions.customMessage || apiError.getUserFriendlyMessage();

  // 记录错误
  if (mergedOptions.logToConsole) {
    console.error("API错误:", apiError);

    // 在开发环境中，可以记录更多详细信息
    if (import.meta.env.DEV) {
      console.error("错误详情:", apiError.data);
    }
  }

  // 显示通知
  if (mergedOptions.showNotification) {
    // 使用通知服务显示错误通知
    showError(message, mergedOptions.title);
  }
}

/**
 * 创建错误处理函数
 * @param options 错误通知选项
 * @returns 错误处理函数
 */
export function createErrorHandler(options: ErrorNotificationOptions = {}) {
  return (error: unknown) => handleErrorWithNotification(error, options);
}
