// Shared gradients
// This file contains gradient definitions used across multiple components

:root {
  // Card gradients
  --gradient-card: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  
  // Selected item gradient
  --gradient-selected: linear-gradient(
    135deg,
    rgba(26, 54, 93, 0.9) 0%,
    rgba(26, 54, 93, 0.7) 100%
  );
  
  // Background gradient
  --gradient-background: linear-gradient(
    to bottom right,
    #6f7985 0%,
    #909caa 20%,
    #d1c2bd 60%,
    #f0c6b8 80%,
    #e29f84 100%
  );
  
  // Header gradient
  --gradient-header: linear-gradient(
    to right,
    rgba(111, 121, 133, 0.1) 0%,
    rgba(144, 156, 170, 0.1) 50%,
    rgba(209, 194, 189, 0.1) 100%
  );
  
  // Input container gradient
  --gradient-input: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  
  // Button gradient
  --gradient-button-primary: linear-gradient(
    135deg,
    #4a5568 0%,
    #1a365d 100%
  );
  
  // Button secondary gradient
  --gradient-button-secondary: linear-gradient(
    135deg,
    #6f7985 0%,
    #909caa 50%,
    #1a365d 100%
  );
  
  // Divider gradient
  --gradient-divider: linear-gradient(
    to right,
    #d1c2bd,
    #f0c6b8
  );
  
  // Title gradient
  --gradient-title: linear-gradient(
    135deg,
    #2d3748 0%,
    #4a5568 50%,
    #1a365d 100%
  );
  
  // Range gradient
  --gradient-range: linear-gradient(
    135deg,
    rgba(79, 209, 197, 0.2) 0%,
    rgba(79, 209, 197, 0.1) 100%
  );
}
