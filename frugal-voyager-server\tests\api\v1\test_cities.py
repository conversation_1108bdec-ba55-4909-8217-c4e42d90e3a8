"""
城市API测试。
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from app.schemas.city import City, CityRequest


@pytest.mark.parametrize(
    "position, expected_status_code",
    [
        ("杭州", 200),
        ("", 400),  # 空字符串应该返回400（验证错误）
    ],
)
def test_ai_recommend_cities(client: TestClient, position, expected_status_code):
    """
    测试AI推荐城市API。
    """
    # 模拟CityService.get_ai_city_recommendations方法
    with patch(
        "app.api.v1.endpoints.cities.CityService.get_ai_city_recommendations"
    ) as mock_get:
        # 设置模拟返回值
        mock_cities = [
            City(
                id="tokyo",
                name="日本东京",
                description="东京是一座充满活力的城市，融合了传统文化与现代科技。",
                rating="4.8",
                cost="￥500/天",
                recommendedStay="5-7 天",
                imageUrl="https://example.com/tokyo.jpg",
            )
        ]
        mock_get.return_value = mock_cities

        # 发送请求
        response = client.post(
            "/api/v1/cities/recommend",
            json={"position": position},
        )

        # 验证状态码
        assert response.status_code == expected_status_code

        # 如果请求成功，验证返回的数据
        if expected_status_code == 200:
            data = response.json()
            assert isinstance(data, list)
            assert len(data) > 0
            assert "id" in data[0]
            assert "name" in data[0]
            assert "description" in data[0]

            # 验证模拟函数被调用
            mock_get.assert_called_once()
            request = mock_get.call_args[0][0]
            assert isinstance(request, CityRequest)
            assert request.position == position
