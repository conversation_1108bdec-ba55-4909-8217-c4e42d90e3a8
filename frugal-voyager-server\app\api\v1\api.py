from fastapi import APIRouter
from app.api.v1.endpoints import cities, tickets, travel_guides, config

api_router = APIRouter()

# 注册API路由
api_router.include_router(config.router, prefix="/config", tags=["config"])
api_router.include_router(cities.router, prefix="/cities", tags=["cities"])
api_router.include_router(tickets.router, prefix="/tickets", tags=["tickets"])
api_router.include_router(
    travel_guides.router, prefix="/travel-guides", tags=["travel-guides"]
)
