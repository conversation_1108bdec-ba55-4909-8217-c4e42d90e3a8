import datetime
from typing import List
from pydantic import BaseModel, Field, ConfigDict

# 获取当天日期转换为 YYYY-MM-DD 格式
today = datetime.datetime.now().strftime("%Y-%m-%d")


class DateInfo(BaseModel):
    """车票可用性的日期信息。"""

    date: str
    day: str
    weekday: str

    model_config = ConfigDict(
        json_schema_extra={"example": {"date": today, "day": "15", "weekday": "周六"}}
    )


class SeatInfo(BaseModel):
    """座位信息。"""

    name: str
    count: int
    price: int

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "二等座",
                "count": 10,
                "price": 553,
            }
        },
    )


class Ticket(BaseModel):
    """火车票信息。"""

    id: str
    departure_time: str = Field(..., alias="departureTime")
    departure_station: str = Field(..., alias="departureStation")
    arrival_time: str = Field(..., alias="arrivalTime")
    arrival_station: str = Field(..., alias="arrivalStation")
    duration: str
    price: str
    train_number: str = Field(..., alias="trainNumber")
    tags: List[SeatInfo]

    model_config = ConfigDict(
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "id": "G105",
                "departureTime": "08:30",
                "departureStation": "北京南",
                "arrivalTime": "13:50",
                "arrivalStation": "上海虹桥",
                "duration": "5小时20分",
                "price": "¥553",
                "trainNumber": "G105",
                "tags": [
                    {"name": "二等座", "count": 10, "price": 553},
                    {"name": "一等座", "count": 5, "price": 933},
                ],
            }
        },
    )


class TicketRequest(BaseModel):
    """车票搜索的请求模型。"""

    city_name: str = Field(
        ..., description="目的地城市名称，如'上海'、'北京'等", min_length=1
    )
    departure_date: str = Field(
        ...,
        description="出发日期，格式为YYYY-MM-DD",
        min_length=1,
        pattern=r"^\d{4}-\d{2}-\d{2}$",
    )
    return_date: str = Field(None, description="返回日期，格式为YYYY-MM-DD（可选）")
    departure_city: str = Field(
        "杭州", description="出发城市名称，如'杭州'、'北京'等（可选，默认为'杭州'）"
    )

    # 为了向后兼容，保留city_id字段
    city_id: str = Field(None, description="已废弃，请使用city_name")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "city_name": "上海",
                "departure_date": today,
                "return_date": today,
                "departure_city": "杭州",
            }
        }
    )


class TicketResponse(BaseModel):
    """车票搜索的响应模型。"""

    departure_tickets: List[Ticket] = Field(..., alias="departureTickets")
    return_tickets: List[Ticket] = Field([], alias="returnTickets")

    model_config = ConfigDict(populate_by_name=True)
