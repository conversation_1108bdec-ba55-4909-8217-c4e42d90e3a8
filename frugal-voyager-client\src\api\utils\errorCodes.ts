/**
 * 错误代码常量
 *
 * 这些错误代码与服务器端的错误代码对应，但前端显示的错误消息应该更加用户友好，
 * 并且不应该暴露具体的技术细节。
 */

/**
 * 错误代码枚举
 */
export enum ErrorCode {
  // 通用错误
  INTERNAL_SERVER_ERROR = "E500",
  INVALID_REQUEST = "E400",
  RESOURCE_NOT_FOUND = "E404",

  // 服务特定错误
  CITY_NOT_FOUND = "E4001",
  AI_SERVICE_ERROR = "E5001",
  MCP_SERVICE_ERROR = "E5002",
  TICKET_SERVICE_ERROR = "E5003",
  EXTERNAL_API_ERROR = "E5004",

  // 票务特定错误
  NO_TICKETS_FOUND = "E4010",
  INVALID_DATE_RANGE = "E4011",
  INVALID_CITY_PAIR = "E4012",
}

/**
 * 用户友好的错误消息映射
 * 这些消息应该是通用的，不暴露具体的技术细节
 */
export const errorMessages: Record<string, string> = {
  // 通用错误
  [ErrorCode.INTERNAL_SERVER_ERROR]: "服务暂时不可用，请稍后再试",
  [ErrorCode.INVALID_REQUEST]: "请求无效，请检查输入",
  [ErrorCode.RESOURCE_NOT_FOUND]: "未找到请求的资源",

  // 服务特定错误
  [ErrorCode.CITY_NOT_FOUND]: "未找到指定的城市",
  [ErrorCode.AI_SERVICE_ERROR]: "推荐服务暂时不可用，请稍后再试",
  [ErrorCode.MCP_SERVICE_ERROR]: "旅游规划服务暂时不可用，请稍后再试",
  [ErrorCode.TICKET_SERVICE_ERROR]: "票务服务暂时不可用，请稍后再试",
  [ErrorCode.EXTERNAL_API_ERROR]: "外部服务暂时不可用，请稍后再试",

  // 票务特定错误
  [ErrorCode.NO_TICKETS_FOUND]: "未找到符合条件的车票，请尝试修改目的地或日期",
  [ErrorCode.INVALID_DATE_RANGE]: "日期范围无效，请选择有效的日期范围",
  [ErrorCode.INVALID_CITY_PAIR]:
    "出发城市和目的地城市之间可能没有直达列车，请尝试选择其他城市",

  // 默认错误消息
  default: "操作失败，请稍后再试",
};

/**
 * 获取用户友好的错误消息
 * @param errorCode 错误代码
 * @returns 用户友好的错误消息
 */
export function getUserFriendlyErrorMessage(errorCode?: string): string {
  if (!errorCode) {
    return errorMessages.default;
  }

  return errorMessages[errorCode] || errorMessages.default;
}
