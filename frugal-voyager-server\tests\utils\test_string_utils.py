"""
字符串处理工具函数测试。
"""
import pytest
from app.utils.string_utils import (
    remove_extra_whitespace,
    extract_between_markers,
    truncate_text,
)


def test_remove_extra_whitespace():
    """测试移除多余的空白字符。"""
    # 测试多个空格
    text_with_spaces = "这是  一些   文本"
    result = remove_extra_whitespace(text_with_spaces)
    assert result == "这是 一些 文本"

    # 测试换行和制表符
    text_with_newlines = "这是\n一些\t文本"
    result = remove_extra_whitespace(text_with_newlines)
    assert result == "这是 一些 文本"

    # 测试前后空白
    text_with_padding = "  这是一些文本  "
    result = remove_extra_whitespace(text_with_padding)
    assert result == "这是一些文本"


def test_extract_between_markers():
    """测试从文本中提取两个标记之间的内容。"""
    # 测试正常情况
    text_with_markers = "这是[开始]要提取的内容[结束]后面的文本"
    result = extract_between_markers(text_with_markers, "[开始]", "[结束]")
    assert result == "要提取的内容"

    # 测试没有结束标记
    text_without_end = "这是[开始]要提取的内容"
    result = extract_between_markers(text_without_end, "[开始]", "[结束]")
    assert result is None

    # 测试没有开始标记
    text_without_start = "这是要提取的内容[结束]"
    result = extract_between_markers(text_without_start, "[开始]", "[结束]")
    assert result is None

    # 测试空文本
    empty_text = ""
    result = extract_between_markers(empty_text, "[开始]", "[结束]")
    assert result is None


def test_truncate_text():
    """测试截断文本到指定长度。"""
    # 测试不需要截断的情况
    short_text = "这是一段短文本"
    result = truncate_text(short_text, 20)
    assert result == short_text

    # 测试需要截断的情况
    long_text = "这是一段很长的文本，需要被截断"
    result = truncate_text(long_text, 10)
    assert result == "这是一段很长的..."
    assert len(result) == 10

    # 测试自定义后缀
    result = truncate_text(long_text, 10, suffix="...")
    assert result == "这是一段很长的..."
    assert len(result) == 10

    # 测试空文本
    empty_text = ""
    result = truncate_text(empty_text, 10)
    assert result == ""
