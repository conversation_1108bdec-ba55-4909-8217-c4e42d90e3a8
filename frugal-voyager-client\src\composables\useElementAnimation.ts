import { ref, onMounted, onUnmounted, type Ref } from "vue";

/**
 * 元素居中和动画控制
 * 用于控制元素的居中定位和显示/隐藏动画
 *
 * @param options 配置选项
 * @returns 元素动画相关的状态和方法
 */
export function useElementAnimation(options: {
  mainRef?: Ref<HTMLElement | null>;
  headerRef?: Ref<HTMLElement | null>;
}) {
  const { mainRef, headerRef } = options;

  // 元素引用
  const mainTitleRef = ref<HTMLElement | null>(null);
  const originInputContainerRef = ref<HTMLElement | null>(null);
  const inboxContainerRef = ref<HTMLElement | null>(null);

  // 内容框是否可见
  const inboxVisible = ref(false);

  /**
   * 将主标题和起始地输入框居中
   */
  const centerElements = () => {
    // 如果inbox是可见的，不执行居中操作
    if (inboxVisible.value) return;

    if (
      !mainRef?.value ||
      !mainTitleRef.value ||
      !originInputContainerRef.value ||
      !headerRef?.value
    )
      return;

    const mainHeight = mainRef.value.offsetHeight;
    const titleHeight = mainTitleRef.value.offsetHeight;
    const inputHeight = originInputContainerRef.value.offsetHeight;
    const headerHeight = headerRef.value.offsetHeight;

    // 计算垂直居中的位置，考虑header的高度
    const totalHeight = titleHeight + inputHeight;
    const availableHeight = mainHeight - headerHeight;
    let topPosition = Math.max(
      0,
      (availableHeight - totalHeight) / 2 - 2 * headerHeight
    );

    // Firefox兼容性修复：限制最大偏移量，防止元素移出可视区域
    const maxOffset = Math.min(availableHeight * 0.3, 150); // 最大偏移不超过可用高度的30%或150px
    topPosition = Math.min(topPosition, maxOffset);

    // 确保偏移量为有效数值
    if (isNaN(topPosition) || !isFinite(topPosition)) {
      topPosition = 0;
    }

    // 应用变换
    mainTitleRef.value.style.transform = `translateY(${topPosition}px)`;
    originInputContainerRef.value.style.transform = `translateY(${topPosition}px)`;
  };

  /**
   * 显示内容框
   */
  const showInbox = () => {
    if (
      inboxVisible.value ||
      !inboxContainerRef.value ||
      !mainTitleRef.value ||
      !originInputContainerRef.value
    )
      return;

    inboxContainerRef.value.style.display = "block";

    // 使用setTimeout确保display:block生效后再添加动画
    setTimeout(() => {
      if (!inboxContainerRef.value) return;
      inboxContainerRef.value.style.opacity = "1";
      inboxContainerRef.value.style.transform = "translateY(0)";

      // 移动标题和输入框到顶部
      if (mainTitleRef.value && originInputContainerRef.value) {
        mainTitleRef.value.style.transform = "translateY(0)";
        originInputContainerRef.value.style.transform = "translateY(0)";

        // 确保输入框在inbox之外，设置固定位置
        originInputContainerRef.value.style.position = "relative";
        originInputContainerRef.value.style.zIndex = "100";
      }

      inboxVisible.value = true;
    }, 50);
  };

  /**
   * 隐藏内容框
   */
  const hideInbox = () => {
    if (!inboxVisible.value || !inboxContainerRef.value) return;

    inboxContainerRef.value.style.opacity = "0";
    inboxContainerRef.value.style.transform = "translateY(20px)";

    // 先将inboxVisible设置为false，这样centerElements函数就能正常执行
    inboxVisible.value = false;

    // 等待动画完成后隐藏元素并恢复样式
    setTimeout(() => {
      if (!inboxContainerRef.value) return;
      inboxContainerRef.value.style.display = "none";

      // 恢复输入框的原始样式
      if (originInputContainerRef.value) {
        // 清除可能影响居中的样式
        originInputContainerRef.value.style.position = "";
        originInputContainerRef.value.style.zIndex = "";

        // 恢复标题和输入框到中间位置
        centerElements();
      }
    }, 800);
  };

  /**
   * 设置内容区域的滚动状态
   * @param enableScroll 是否启用滚动
   */
  const setContentScrolling = (enableScroll: boolean) => {
    const inboxContent = document.querySelector(".inbox-content");
    if (inboxContent) {
      (inboxContent as HTMLElement).style.overflowY = enableScroll
        ? "auto"
        : "hidden";
    }
  };

  // 生命周期钩子
  onMounted(() => {
    // 页面加载完成后执行居中
    centerElements();

    // 添加窗口大小变化监听
    window.addEventListener("resize", centerElements);
  });

  onUnmounted(() => {
    // 移除窗口大小变化监听
    window.removeEventListener("resize", centerElements);
  });

  return {
    // 状态
    inboxVisible,

    // 引用
    mainTitleRef,
    originInputContainerRef,
    inboxContainerRef,

    // 方法
    centerElements,
    showInbox,
    hideInbox,
    setContentScrolling,
  };
}
