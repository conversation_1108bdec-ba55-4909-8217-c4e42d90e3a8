import { ref } from "vue";
import type TimeSelectionView from "@/components/TimeSelectionView/TimeSelectionView.vue";
import type ItineraryPlanView from "@/components/ItineraryPlanView/ItineraryPlanView.vue";

/**
 * 组件引用管理
 * 用于管理子组件的引用和初始化
 *
 * @returns 组件引用相关的状态和方法
 */
export function useComponentRefs() {
  // 组件引用
  const timeSelectionRef = ref<InstanceType<typeof TimeSelectionView> | null>(
    null
  );
  const itineraryPlanRef = ref<InstanceType<typeof ItineraryPlanView> | null>(
    null
  );

  /**
   * 初始化时间选择组件
   * @param cityName 可选的目的地城市名称
   * @param departureCity 可选的出发城市名称
   */
  const initTimeSelectionComponent = (
    cityName?: string,
    departureCity?: string
  ) => {
    if (timeSelectionRef.value) {
      // 使用setTimeout确保DOM已更新
      setTimeout(() => {
        console.log("调用 TimeSelectionView 组件的 initializeComponent 方法");
        if (cityName) {
          console.log(`传递目的地城市名称: ${cityName}`);
          if (departureCity) {
            console.log(`传递出发城市名称: ${departureCity}`);
            timeSelectionRef.value?.initializeComponent(
              cityName,
              departureCity
            );
          } else {
            console.log("没有传递出发城市名称，使用默认值");
            timeSelectionRef.value?.initializeComponent(cityName);
          }
        } else {
          console.log("没有传递城市名称，使用默认值");
          timeSelectionRef.value?.initializeComponent();
        }
      }, 100);
    } else {
      console.warn("timeSelectionRef.value 为空，无法初始化组件");
    }
  };

  /**
   * 初始化行程计划组件
   * @param cityName 可选的城市名称
   * @param selectedDates 可选的选择日期
   * @param departureTrainInfo 可选的去程火车信息
   * @param returnTrainInfo 可选的返程火车信息
   */
  const initItineraryPlanComponent = (
    cityName?: string,
    selectedDates?: string[],
    departureTrainInfo?: {
      trainNumber: string;
      departureTime: string;
      arrivalTime: string;
    },
    returnTrainInfo?: {
      trainNumber: string;
      departureTime: string;
      arrivalTime: string;
    }
  ) => {
    if (itineraryPlanRef.value) {
      // 使用setTimeout确保DOM已更新
      setTimeout(() => {
        if (cityName && selectedDates) {
          console.log(
            `初始化行程计划组件，城市: ${cityName}, 日期: ${selectedDates.join(
              " 至 "
            )}`
          );

          // 如果有火车票信息，打印出来
          if (departureTrainInfo) {
            console.log(
              `去程火车信息: ${departureTrainInfo.trainNumber}, 出发时间: ${departureTrainInfo.departureTime}, 到达时间: ${departureTrainInfo.arrivalTime}`
            );
          }
          if (returnTrainInfo) {
            console.log(
              `返程火车信息: ${returnTrainInfo.trainNumber}, 出发时间: ${returnTrainInfo.departureTime}, 到达时间: ${returnTrainInfo.arrivalTime}`
            );
          }

          // 调用组件的初始化方法，传递所有参数
          itineraryPlanRef.value?.initializeComponent(
            cityName,
            selectedDates,
            departureTrainInfo,
            returnTrainInfo
          );
        } else {
          console.log("没有传递城市名称或日期，使用默认值");
          itineraryPlanRef.value?.initializeComponent();
        }
      }, 100);
    } else {
      console.warn("itineraryPlanRef.value 为空，无法初始化组件");
    }
  };

  return {
    // 引用
    timeSelectionRef,
    itineraryPlanRef,

    // 方法
    initTimeSelectionComponent,
    initItineraryPlanComponent,
  };
}
