{"name": "frugal-voyager-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "dayjs": "^1.11.13", "pinia": "^3.0.2", "sass": "^1.87.0", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@types/node": "^22.15.3", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.3.1", "vue-tsc": "^2.2.8"}}