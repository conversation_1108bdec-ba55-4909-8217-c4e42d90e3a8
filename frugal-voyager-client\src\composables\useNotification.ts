import { reactive } from "vue";

// 通知类型
export type NotificationType = "info" | "success" | "warning" | "error";

// 通知位置
export type NotificationPosition = "top" | "center" | "bottom";

// 通知选项
export interface NotificationOptions {
  title?: string;
  message: string;
  type?: NotificationType;
  duration?: number;
  position?: NotificationPosition;
}

/**
 * 通知管理
 * 用于管理全局通知弹窗的显示和隐藏
 *
 * @returns 通知相关的状态和方法
 */
export function useNotification() {
  // 通知状态
  const notification = reactive<{
    show: boolean;
    title?: string;
    message: string;
    type: NotificationType;
    duration: number;
    position: NotificationPosition;
  }>({
    show: false,
    message: "",
    type: "info",
    duration: 5000, // 延长显示时间为5秒
    position: "top", // 默认位置改为顶部
  });

  /**
   * 显示通知
   * @param options 通知选项
   */
  const showNotification = (options: NotificationOptions) => {
    // 如果已经显示，先隐藏
    if (notification.show) {
      notification.show = false;
      // 等待动画结束后再显示新通知
      setTimeout(() => {
        Object.assign(notification, {
          show: true,
          title: options.title || "",
          message: options.message,
          type: options.type || "info",
          duration: options.duration !== undefined ? options.duration : 5000,
          position: options.position || "top", // 默认位置为顶部
        });
      }, 300);
    } else {
      Object.assign(notification, {
        show: true,
        title: options.title || "",
        message: options.message,
        type: options.type || "info",
        duration: options.duration !== undefined ? options.duration : 5000,
        position: options.position || "top", // 默认位置为顶部
      });
    }
  };

  /**
   * 隐藏通知
   */
  const hideNotification = () => {
    notification.show = false;
  };

  /**
   * 显示信息通知
   * @param message 消息内容
   * @param title 消息标题
   * @param options 其他选项
   */
  const info = (
    message: string,
    title?: string,
    options: Partial<
      Omit<NotificationOptions, "message" | "title" | "type">
    > = {}
  ) => {
    showNotification({
      title,
      message,
      type: "info",
      ...options,
    });
  };

  /**
   * 显示成功通知
   * @param message 消息内容
   * @param title 消息标题
   * @param options 其他选项
   */
  const success = (
    message: string,
    title?: string,
    options: Partial<
      Omit<NotificationOptions, "message" | "title" | "type">
    > = {}
  ) => {
    showNotification({
      title,
      message,
      type: "success",
      ...options,
    });
  };

  /**
   * 显示警告通知
   * @param message 消息内容
   * @param title 消息标题
   * @param options 其他选项
   */
  const warning = (
    message: string,
    title?: string,
    options: Partial<
      Omit<NotificationOptions, "message" | "title" | "type">
    > = {}
  ) => {
    showNotification({
      title,
      message,
      type: "warning",
      ...options,
    });
  };

  /**
   * 显示错误通知
   * @param message 消息内容
   * @param title 消息标题
   * @param options 其他选项
   */
  const error = (
    message: string,
    title?: string,
    options: Partial<
      Omit<NotificationOptions, "message" | "title" | "type">
    > = {}
  ) => {
    showNotification({
      title,
      message,
      type: "error",
      ...options,
    });
  };

  return {
    notification,
    showNotification,
    hideNotification,
    info,
    success,
    warning,
    error,
  };
}
