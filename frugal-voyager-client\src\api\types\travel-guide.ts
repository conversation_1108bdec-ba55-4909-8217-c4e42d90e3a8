/**
 * 旅游指南相关类型定义
 */

/**
 * 指南项目基础接口
 */
export interface BaseGuideItem {
  /** 项目ID */
  id: string;
  /** 项目类型 */
  type: string;
  /** 时间段 */
  time: string;
}

/**
 * 交通项目
 */
export interface Transportation extends BaseGuideItem {
  /** 项目类型，固定为"transportation" */
  type: "transportation";
  /** 交通方式图标 */
  icon: string;
  /** 交通时长 */
  duration: string;
  /** 交通详情 */
  detail: string;
}

/**
 * 景点项目
 */
export interface Attraction extends BaseGuideItem {
  /** 项目类型，固定为"attraction" */
  type: "attraction";
  /** 景点名称 */
  name: string;
  /** 景点描述 */
  description: string;
  /** 门票价格 */
  ticketPrice: string;
  /** 推荐游览时间 */
  recommendedStay: string;
  /** 景点图片URL */
  imageUrl: string;
  /** 显示位置，可以是"left"或"right" */
  position: "left" | "right";
}

/**
 * 指南项目类型（联合类型）
 */
export type GuideItem = Transportation | Attraction;

/**
 * 火车票信息
 */
export interface TrainInfo {
  /** 去程火车车次 */
  departure_train?: string;
  /** 去程出发时间 */
  departure_time?: string;
  /** 去程到达时间 */
  arrival_time?: string;
  /** 返程火车车次 */
  return_train?: string;
  /** 返程出发时间 */
  return_departure_time?: string;
  /** 返程到达时间 */
  return_arrival_time?: string;
}

/**
 * 生成旅游指南请求参数
 */
export interface GenerateGuideRequest {
  /** 城市名称 */
  city: string;
  /** 旅游天数，默认为2天 */
  days?: number;
  /** 火车票信息 */
  trainInfo?: TrainInfo;
}
