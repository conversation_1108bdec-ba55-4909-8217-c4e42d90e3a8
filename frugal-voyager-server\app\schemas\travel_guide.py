from typing import List, Literal, Optional
from pydantic import BaseModel, Field, ConfigDict


class BaseGuideItem(BaseModel):
    """旅行指南项目的基类。"""

    id: str
    type: str
    time: str


class Transportation(BaseGuideItem):
    """旅行指南中的交通项目。"""

    type: Literal["transportation"]
    icon: str
    duration: str
    detail: str

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "transport-initial",
                "type": "transportation",
                "time": "07:00 - 08:00",
                "icon": "✈️",
                "duration": "1小时",
                "detail": "从北京首都机场飞往上海虹桥机场",
            }
        }
    )


class Attraction(BaseGuideItem):
    """旅行指南中的景点项目。"""

    type: Literal["attraction"]
    name: str
    description: str
    ticket_price: str = Field(..., alias="ticketPrice")
    recommended_stay: str = Field(..., alias="recommendedStay")
    image_url: str = Field(..., alias="imageUrl")
    position: Literal["left", "right"]

    model_config = ConfigDict(
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "id": "shanghai-hotel",
                "type": "attraction",
                "time": "09:00 - 10:30",
                "name": "上海迎宾馆",
                "description": "上海迎宾馆是上海地标性建筑，融合了中西建筑风格，可以欣赏到独特的建筑艺术。",
                "ticketPrice": "免费",
                "recommendedStay": "1.5小时",
                "imageUrl": "https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "position": "left",
            }
        },
    )


class TravelGuideRequest(BaseModel):
    """生成旅行指南的请求模型。"""

    city_id: str = Field(..., description="需要生成旅行指南的城市ID")
    duration: int = Field(..., description="行程天数")
    interests: Optional[List[str]] = Field(None, description="用户兴趣列表")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "city_id": "shanghai",
                "duration": 2,
                "interests": ["history", "food", "architecture"],
            }
        }
    )


# 指南项目的联合类型
GuideItem = Transportation | Attraction
