@use "@/assets/scss/utils/scrollbars" as *;
@use "@/assets/scss/components/shared-components" as *;

/* 计划安排模块样式 */
.itinerary-container {
  @extend .split-container;
  position: relative;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top-color: #4fd1c5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-circle-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border: 4px solid transparent;
  border-top-color: #ed8936;
  border-radius: 50%;
  animation: spin 1.5s linear infinite reverse;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #1a365d;
  font-weight: 600;
}

/* 当加载时，降低内容的不透明度 */
.timeline-container.loading {
  opacity: 0.5;
  pointer-events: none;
}

.timeline-container {
  @extend .content-container;
  position: relative;
  overflow-y: scroll;
  @include hide-scrollbar;
}

.timeline {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: rgba(68, 89, 120, 1);
  transform: translateX(-50%);
  z-index: 0;
}

/* 移除不需要的连接线 */
.timeline-connector {
  display: none;
}

/* 确保时间点之间的连接 */
.timeline-item,
.transportation-item {
  position: relative;
}

/* 确保每个时间线项目和交通方式项目之间有连接线 */
.timeline-item {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;

  &::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 16px;
    bottom: -30px;
    width: 4px;
    background: rgba(68, 89, 120, 1);
    transform: translateX(-50%);
    z-index: 1;
  }

  &:last-child::after {
    display: none;
  }
}

.transportation-item {
  display: flex;
  justify-content: center;
  position: relative;
  margin: 25px 0;
  z-index: 2;

  &::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 12px;
    bottom: -30px;
    width: 4px;
    background: rgba(68, 89, 120, 1);
    transform: translateX(-50%);
    z-index: 1;
  }

  &::before {
    content: "";
    position: absolute;
    left: 50%;
    top: -12px;
    height: 30px;
    width: 4px;
    background: rgba(68, 89, 120, 1);
    transform: translateX(-50%);
    z-index: 1;
  }
}

.transportation-dot {
  position: absolute;
  left: 50%;
  top: -17px;
  width: 12px;
  height: 12px;
  background: #ed8936;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 3;
  box-shadow: 0 0 0 4px rgba(237, 137, 54, 0.2);
}

.timeline-content {
  width: 100%;
  padding: 20px 0;
  position: relative;
  z-index: 2;
}

.timeline-dot {
  position: absolute;
  left: 50%;
  top: 0;
  width: 16px;
  height: 16px;
  background: #4fd1c5;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 3;
  box-shadow: 0 0 0 4px rgba(79, 209, 197, 0.2);
}

.timeline-time {
  position: absolute;
  background: rgba(26, 54, 93, 0.8);
  color: white;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  z-index: 5;
}

/* 当卡片在左侧时，时间显示在右侧 */
.timeline-card.left + .timeline-time {
  left: 57%;
  top: 0px;
}

/* 当卡片在右侧时，时间显示在左侧 */
.timeline-card.right + .timeline-time {
  right: 57%;
  top: 0px;
}

.timeline-card {
  width: 42%;
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  }

  &.left {
    margin-right: auto;

    &:after {
      content: "";
      position: absolute;
      top: 15px;
      width: 12px;
      height: 12px;
      background: inherit;
      transform: rotate(45deg);
      right: -6px;
    }
  }

  &.right {
    margin-left: auto;

    &:after {
      content: "";
      position: absolute;
      top: 15px;
      width: 12px;
      height: 12px;
      background: inherit;
      transform: rotate(45deg);
      left: -6px;
    }
  }

  &.expanded {
    .timeline-card-details {
      height: auto;
      opacity: 1;
    }

    .expand-icon {
      transform: rotate(180deg);
    }
  }
}

.timeline-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .expand-icon {
    font-size: 18px;
    color: #4a5568;
    transition: transform 0.3s ease;
  }
}

.timeline-card-content {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

.timeline-card-details {
  height: 0;
  overflow: hidden;
  opacity: 0;
  transition: height 0.3s ease, opacity 0.3s ease;
}

.timeline-card-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 12px;
  color: #718096;
}

.timeline-card-image {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.transportation {
  position: relative;
  left: 0;
  margin-top: 15px;
  background: rgba(255, 255, 255, 0.8);
  padding: 4px 6px;
  border-radius: 10px;
  font-size: 12px;
  color: #4a5568;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 4;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: min-content;
  width: auto;
  min-height: 24px;
  height: auto;
  overflow: hidden;

  &:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  }

  &.expanded {
    width: auto;
    min-width: 100px;
    max-width: 150px;
    height: auto;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);

    .transportation-detail {
      opacity: 1;
      max-height: 100px;
      margin-top: 4px;
    }
  }
}

.transportation-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  width: auto;
  min-height: 20px;
  white-space: nowrap;
  padding: 0;

  &::after {
    content: "▼";
    font-size: 10px;
    color: #4a5568;
    margin-left: 4px;
    transition: transform 0.3s ease;
  }
}

.transportation.expanded .transportation-header::after {
  content: "▲";
}

.transportation-icon {
  font-size: 14px;
  color: #1a365d;
}

.transportation-time {
  font-weight: 600;
  font-size: 11px;
  color: #1a365d;
  white-space: nowrap;
}

.transportation-detail {
  font-size: 10px;
  color: #718096;
  text-align: center;
  line-height: 1.2;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: opacity 0.3s ease, max-height 0.3s ease;
  width: 100%;
  margin-top: 0;
  white-space: normal;
  padding: 0;
  word-break: break-word;
}

/* 日期选择器样式 */
.day-selector {
  @extend .selector-container;
}

.day-selector-header {
  @extend .selector-header;
}

.day-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 15px;
  text-align: center;
}

.day-selector-content {
  @extend .selector-content;
  @include hide-scrollbar;
}

.day-item {
  @extend .selector-item;
}

.day-number {
  @extend .selector-primary;
}

.day-label {
  @extend .selector-secondary;
}

/* 火车票信息样式 */
.train-info-section {
  padding: 10px;
  border-top: 1px solid #e2e8f0;
  margin-top: 15px;

  .train-info-header {
    font-size: 14px;
    font-weight: 600;
    color: #1a365d;
    text-align: center;
    margin-bottom: 10px;
  }

  .train-info-item {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e2e8f0;

    &:last-child {
      margin-bottom: 0;
    }

    .train-info-title {
      font-size: 14px;
      font-weight: 600;
      color: #4299e1;
      margin-bottom: 5px;
      text-align: center;
    }

    .train-info-detail {
      font-size: 12px;
      color: #4a5568;

      div {
        margin-bottom: 3px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
