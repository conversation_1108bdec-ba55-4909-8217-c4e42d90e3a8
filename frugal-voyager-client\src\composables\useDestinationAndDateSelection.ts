import { ref } from "vue";

/**
 * 目的地和日期选择
 * 用于管理目的地和日期选择的状态和处理逻辑
 *
 * @param onDestinationSelect 目的地选择后的回调函数
 * @param onDatesSelect 日期选择后的回调函数
 * @returns 目的地和日期选择相关的状态和方法
 */
export function useDestinationAndDateSelection(
  onDestinationSelect?: (destination: string) => void,
  onDatesSelect?: (dates: string[]) => void
) {
  // 选择的日期
  const selectedDates = ref<string[]>([]);

  // 选择的目的地
  const selectedDestination = ref<string>("");

  /**
   * 处理目的地选择
   * @param destination 选择的目的地
   */
  const handleDestinationSelect = (destination: string) => {
    selectedDestination.value = destination;
    console.log(`选择了目的地: ${destination}`);

    // 调用外部回调
    if (onDestinationSelect) {
      onDestinationSelect(destination);
    }
  };

  /**
   * 处理日期选择
   * @param dates 选择的日期数组
   */
  const handleDatesSelected = (dates: string[]) => {
    selectedDates.value = dates;
    console.log(`选择了日期: ${dates.join(" 至 ")}`);

    // 调用外部回调
    if (onDatesSelect) {
      onDatesSelect(dates);
    }
  };

  /**
   * 清除选择
   */
  const clearSelections = () => {
    selectedDates.value = [];
    selectedDestination.value = "";
  };

  return {
    // 状态
    selectedDates,
    selectedDestination,

    // 方法
    handleDestinationSelect,
    handleDatesSelected,
    clearSelections,
  };
}
