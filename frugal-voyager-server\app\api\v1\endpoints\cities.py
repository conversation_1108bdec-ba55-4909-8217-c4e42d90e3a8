from typing import List
from fastapi import APIRouter
from loguru import logger

from app.schemas.city import City, CityRequest
from app.services.city_service import CityService
from app.utils.exceptions import ServiceException
from app.schemas.errors import ErrorCode

router = APIRouter()


@router.post("/recommend", response_model=List[City], summary="使用AI获取推荐城市")
async def ai_recommend_cities(request: CityRequest):
    """
    使用AI（Langchain + OpenRouter + Tavily）根据用户偏好生成推荐城市。

    - **preferences**: 用户旅行偏好
    - **budget**: 用户预算水平（低、中、高）
    - **duration**: 首选行程天数

    返回AI生成的推荐城市列表。

    注意：需要在环境变量中配置 OPENROUTER_API_KEY 和 TAVILY_API_KEY。
    """
    try:
        logger.info(f"收到AI城市推荐请求: {request}")
        cities = await CityService.get_ai_city_recommendations(request)

        # 检查是否返回了空列表
        if not cities:
            logger.warning("AI城市推荐返回了空列表")
            raise ServiceException(
                detail="无法生成城市推荐，请稍后重试",
                error_code=ErrorCode.AI_SERVICE_ERROR,
            )

        return cities
    except ServiceException:
        # 直接重新抛出已经是ServiceException的异常
        raise
    except Exception as e:
        logger.error(f"生成AI城市推荐时出错: {e}")
        raise ServiceException(
            detail="生成AI城市推荐失败", error_code=ErrorCode.AI_SERVICE_ERROR
        )
