from datetime import datetime, timedelta
import json
import os
import traceback
from typing import List, Dict, Any, Optional
from loguru import logger

from app.schemas.ticket import DateInfo, SeatInfo, Ticket, TicketRequest, TicketResponse
from app.utils.train_api import get_tickets_with_prices
from app.utils.exceptions import ServiceException, ResourceNotFoundException
from app.schemas.errors import ErrorCode
from app.config import settings


class TicketService:
    """票务相关操作的服务。"""

    # 城市ID到车站代码的映射缓存
    _city_to_station_code_map = {}

    # 城市名称到车站代码的映射
    _name_to_station_codes_map = {}

    @staticmethod
    def _load_station_data() -> None:
        """加载车站数据，初始化城市名称到车站代码的映射。"""
        if TicketService._name_to_station_codes_map:
            return  # 已经加载过了

        try:
            # 加载所有车站数据
            all_station_path = os.path.join(
                "app", "utils", "data", "TrainAllStation.json"
            )
            with open(all_station_path, "r", encoding="utf-8") as f:
                all_stations = json.load(f)

            # 加载热门车站数据（优先级更高）
            hot_station_path = os.path.join(
                "app", "utils", "data", "TrainHotStation.json"
            )
            with open(hot_station_path, "r", encoding="utf-8") as f:
                hot_stations = json.load(f)

            # 初始化映射（先加载所有车站）
            for station in all_stations:
                name = station["Name"]
                # 提取城市名称（去掉方向，如"北京东"中的"东"）
                base_name = name
                for direction in ["东", "西", "南", "北"]:
                    if name.endswith(direction):
                        base_name = name[:-1]
                        break

                # 将城市名称映射到车站代码列表
                if base_name not in TicketService._name_to_station_codes_map:
                    TicketService._name_to_station_codes_map[base_name] = []

                # 添加车站代码
                station_info = {
                    "name": name,
                    "code": station["StationCode"],
                    "is_hot": False,
                }
                TicketService._name_to_station_codes_map[base_name].append(station_info)

                # 同时添加完整名称的映射
                if (
                    name != base_name
                    and name not in TicketService._name_to_station_codes_map
                ):
                    TicketService._name_to_station_codes_map[name] = [
                        {"name": name, "code": station["StationCode"], "is_hot": False}
                    ]

            # 覆盖热门车站数据（优先级更高）
            for station in hot_stations:
                name = station["Name"]
                # 提取城市名称
                base_name = name
                for direction in ["东", "西", "南", "北"]:
                    if name.endswith(direction):
                        base_name = name[:-1]
                        break

                # 更新热门车站标记
                if base_name in TicketService._name_to_station_codes_map:
                    # 查找并更新现有条目
                    found = False
                    for station_info in TicketService._name_to_station_codes_map[
                        base_name
                    ]:
                        if station_info["name"] == name:
                            station_info["code"] = station["StationCode"]
                            station_info["is_hot"] = True
                            found = True
                            break

                    # 如果没有找到，添加新条目
                    if not found:
                        station_info = {
                            "name": name,
                            "code": station["StationCode"],
                            "is_hot": True,
                        }
                        TicketService._name_to_station_codes_map[base_name].append(
                            station_info
                        )
                else:
                    # 添加新的城市
                    TicketService._name_to_station_codes_map[base_name] = [
                        {"name": name, "code": station["StationCode"], "is_hot": True}
                    ]

                # 同时添加完整名称的映射
                if name != base_name:
                    TicketService._name_to_station_codes_map[name] = [
                        {"name": name, "code": station["StationCode"], "is_hot": True}
                    ]

            # 统计映射数量
            total_stations = sum(
                len(stations)
                for stations in TicketService._name_to_station_codes_map.values()
            )
            logger.info(
                f"成功加载车站数据，共 {len(TicketService._name_to_station_codes_map)} 个城市，{total_stations} 个车站"
            )
        except Exception as e:
            logger.error(f"加载车站数据时出错: {e}")
            # 使用默认映射
            TicketService._name_to_station_codes_map = {
                "上海": [{"name": "上海", "code": "SHH", "is_hot": True}],
                "北京": [{"name": "北京", "code": "BJP", "is_hot": True}],
                "杭州": [{"name": "杭州", "code": "HZH", "is_hot": True}],
            }
            logger.warning("使用默认城市映射")

    @staticmethod
    def _get_station_codes(city_name: str) -> List[str]:
        """
        根据城市名称获取所有匹配的车站代码。

        Args:
            city_name: 城市名称，如"北京"、"上海"等

        Returns:
            匹配的车站代码列表
        """
        # 确保数据已加载
        TicketService._load_station_data()

        # 尝试直接匹配
        if city_name in TicketService._name_to_station_codes_map:
            # 优先返回热门车站
            stations = TicketService._name_to_station_codes_map[city_name]
            hot_stations = [s["code"] for s in stations if s["is_hot"]]
            if hot_stations:
                logger.debug(f"城市 '{city_name}' 匹配到热门车站: {hot_stations}")
                return hot_stations

            # 如果没有热门车站，返回所有匹配的车站
            all_codes = [s["code"] for s in stations]
            logger.debug(f"城市 '{city_name}' 匹配到车站: {all_codes}")
            return all_codes

        # 尝试模糊匹配
        matched_codes = []
        for name, stations in TicketService._name_to_station_codes_map.items():
            # 如果城市名称包含在键中，或者键包含在城市名称中
            if city_name in name or name in city_name:
                # 优先添加热门车站
                hot_codes = [s["code"] for s in stations if s["is_hot"]]
                matched_codes.extend(hot_codes)

                # 如果没有热门车站，添加所有匹配的车站
                if not hot_codes:
                    matched_codes.extend([s["code"] for s in stations])

                logger.debug(f"城市 '{city_name}' 模糊匹配到 '{name}'")

        if matched_codes:
            return matched_codes

        # 默认返回
        logger.warning(f"无法找到城市 '{city_name}' 对应的车站代码，使用默认值 'SHH'")
        return ["SHH"]  # 默认使用上海

    @staticmethod
    def _get_station_code(city_id: str) -> str:
        """
        根据城市ID获取车站代码（兼容旧接口）。
        返回第一个匹配的车站代码。
        """
        codes = TicketService._get_station_codes(city_id)
        return codes[0] if codes else "SHH"

    @staticmethod
    def _convert_to_ticket(train_info: Dict[str, Any]) -> Ticket:
        """将12306返回的车次信息转换为Ticket对象。"""
        # 构建座位信息列表
        seat_infos = []

        # 检查是否使用了新的 get_tickets_with_prices 接口
        is_new_api = "prices" in train_info and train_info["prices"]

        if is_new_api:
            # 使用新接口，从 prices 字段获取座位信息
            for price_info in train_info["prices"]:
                seat_name = price_info.get("seat_name", "")
                if not seat_name:
                    continue

                # 获取票数
                count_str = price_info.get("num", "0")
                count = 0
                if count_str.isdigit():
                    count = int(count_str)
                elif count_str != "--" and count_str != "*" and count_str != "无":
                    count = 1  # 有票但不知道具体数量

                # 获取票价
                price_value = price_info.get("price", 0)

                # 创建座位信息对象
                seat_info = SeatInfo(name=seat_name, count=count, price=price_value)
                seat_infos.append(seat_info)
        else:
            # 使用旧接口，从各个座位类型字段获取信息
            seat_types = {
                "商务座": train_info.get("swz_num", "--"),
                "一等座": train_info.get("ydz_num", "--"),
                "二等座": train_info.get("edz_num", "--"),
                "软卧": train_info.get("rw_num", "--"),
                "硬卧": train_info.get("yw_num", "--"),
                "硬座": train_info.get("yz_num", "--"),
                "无座": train_info.get("wz_num", "--"),
            }

            # 添加有座位的信息
            for seat_type, availability in seat_types.items():
                if (
                    availability != "--"
                    and availability != "*"
                    and availability != "无"
                ):
                    # 创建座位信息对象（旧接口没有价格信息，默认为0）
                    count = 1  # 默认有票但不知道具体数量
                    if availability.isdigit():
                        count = int(availability)

                    seat_info = SeatInfo(
                        name=seat_type, count=count, price=0  # 旧接口没有价格信息
                    )
                    seat_infos.append(seat_info)

        # 获取价格信息（用于显示在票价字段）
        price = "¥--"
        if is_new_api:
            # 找到最低价格
            min_price = None
            for price_info in train_info["prices"]:
                if price_info.get("price") and (
                    min_price is None or price_info["price"] < min_price
                ):
                    min_price = price_info["price"]

            if min_price is not None:
                price = f"¥{min_price}"

        # 创建Ticket对象
        return Ticket(
            id=train_info["train_code"],
            departureTime=train_info["start_time"],
            departureStation=train_info.get("from_station_name", "未知"),
            arrivalTime=train_info["arrive_time"],
            arrivalStation=train_info.get("to_station_name", "未知"),
            duration=train_info["total_time"],
            price=price,  # 使用解析出的价格信息
            trainNumber=train_info["train_code"],
            tags=seat_infos,  # 使用座位信息对象列表
        )

    @staticmethod
    async def get_available_tickets(request: TicketRequest) -> TicketResponse:
        """
        获取城市的可用车票。

        使用12306 API获取实际车票数据。
        如果提供了return_date，则同时获取返程车票。
        """
        # 获取城市名称（优先使用city_name，如果没有则使用city_id）
        city_name = (
            request.city_name
            if hasattr(request, "city_name") and request.city_name
            else request.city_id
        )

        # 获取出发城市名称
        departure_city = (
            request.departure_city if hasattr(request, "departure_city") else "杭州"
        )

        logger.info(
            f"正在搜索城市的车票: 从 {departure_city} 到 {city_name}, 出发日期: {request.departure_date}"
        )
        if request.return_date:
            logger.info(f"同时搜索返程车票，返程日期: {request.return_date}")

        try:
            # 获取出发站和目的地站的代码
            from_station_codes = TicketService._get_station_codes(departure_city)
            if not from_station_codes:
                logger.warning(
                    f"未找到城市 '{departure_city}' 对应的车站代码，使用默认杭州站"
                )
                from_station = "HZH"  # 默认杭州
                from_station_name = "杭州"
            else:
                from_station = from_station_codes[0]  # 使用第一个车站代码
                from_station_name = departure_city

            to_station_codes = TicketService._get_station_codes(city_name)

            # 如果没有找到匹配的车站，抛出异常
            if not to_station_codes:
                logger.warning(f"未找到城市 '{city_name}' 对应的车站代码")
                raise ResourceNotFoundException(
                    detail=f"未找到城市 '{city_name}' 对应的车站",
                    error_code=ErrorCode.CITY_NOT_FOUND,
                )

            # 获取去程车票
            departure_tickets = await TicketService._get_tickets_for_route(
                from_station=from_station,
                to_station_codes=to_station_codes,
                date=request.departure_date,
                from_station_name=from_station_name,
                to_station_name=city_name,
            )

            # 获取返程车票（如果提供了返程日期）
            return_tickets = []
            if request.return_date:
                # 获取返程目的地的车站代码（即出发城市的车站代码）
                return_to_station_codes = (
                    from_station_codes if from_station_codes else ["HZH"]
                )

                return_tickets = await TicketService._get_tickets_for_route(
                    from_station=to_station_codes[0],  # 使用目的地的第一个车站代码
                    to_station_codes=return_to_station_codes,  # 返回出发城市
                    date=request.return_date,
                    from_station_name=city_name,
                    to_station_name=from_station_name,
                )

            # 创建响应
            return TicketResponse(
                departureTickets=departure_tickets,
                returnTickets=return_tickets,
            )

        except ServiceException:
            # 直接重新抛出已经是ServiceException的异常
            raise
        except ResourceNotFoundException:
            # 直接重新抛出已经是ResourceNotFoundException的异常
            raise
        except Exception as e:
            logger.error(f"获取车票信息时出错: {e}")
            logger.error(traceback.format_exc())
            raise ServiceException(
                detail=f"搜索车票失败: {str(e) if settings.ENV == 'development' else '请稍后重试'}",
                error_code=ErrorCode.TICKET_SERVICE_ERROR,
            )

    @staticmethod
    async def _get_tickets_for_route(
        from_station: str,
        to_station_codes: List[str],
        date: str,
        from_station_name: str,
        to_station_name: str,
    ) -> List[Ticket]:
        """
        获取指定路线的车票信息。

        Args:
            from_station: 出发站代码
            to_station_codes: 目的地站代码列表
            date: 出发日期
            from_station_name: 出发站名称
            to_station_name: 目的地站名称

        Returns:
            车票列表
        """
        # 合并所有目的地车站的查询结果
        all_train_info = []

        # 最多查询前3个车站，避免请求过多
        for to_station in to_station_codes[:3]:
            logger.info(f"查询从 {from_station} 到 {to_station} 的车票，日期: {date}")

            # 调用12306 API获取车票信息（包含价格）
            train_info_list = get_tickets_with_prices(
                fromDate=date,
                fromStation=from_station,
                toStation=to_station,
                purposeCodes="ADULT",
            )

            if train_info_list:
                all_train_info.extend(train_info_list)

        # 使用合并后的结果
        train_info_list = all_train_info

        # 检查是否获取到数据
        if not train_info_list:
            logger.warning(
                f"未找到从 {from_station_name} 到 {to_station_name} 的车票信息，日期: {date}"
            )
            return []

        # 转换为Ticket对象
        tickets = []
        for train_info in train_info_list:
            try:
                # 添加站名信息
                train_info["from_station_name"] = from_station_name
                train_info["to_station_name"] = to_station_name

                # 转换为Ticket对象
                ticket = TicketService._convert_to_ticket(train_info)
                tickets.append(ticket)
            except Exception as e:
                logger.error(f"处理车次信息时出错: {e}")
                continue

        logger.info(
            f"成功获取 {len(tickets)} 个车次信息，从 {from_station_name} 到 {to_station_name}"
        )
        return tickets

    @staticmethod
    def _generate_date_range(start_date_str: str, days: int) -> List[DateInfo]:
        """从给定日期开始生成一系列日期。"""
        start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
        weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

        date_range = []
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            date_info = DateInfo(
                date=current_date.strftime("%Y-%m-%d"),
                day=current_date.strftime("%d"),
                weekday=weekdays[current_date.weekday()],
            )
            date_range.append(date_info)

        return date_range
