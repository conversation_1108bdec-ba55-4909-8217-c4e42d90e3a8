"""
MCP相关的请求和响应模型。
"""

from typing import List
from pydantic import BaseModel, Field, ConfigDict

from app.schemas.travel_guide import GuideItem


class TrainInfo(BaseModel):
    """火车票信息模型，用于旅游规划。"""

    departure_train: str = Field(None, description="去程火车车次，例如'G105'")
    departure_time: str = Field(None, description="去程出发时间，例如'08:30'")
    arrival_time: str = Field(None, description="去程到达时间，例如'10:30'")
    return_train: str = Field(None, description="返程火车车次，例如'G106'")
    return_departure_time: str = Field(None, description="返程出发时间，例如'16:30'")
    return_arrival_time: str = Field(None, description="返程到达时间，例如'18:30'")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "departure_train": "G105",
                "departure_time": "08:30",
                "arrival_time": "10:30",
                "return_train": "G106",
                "return_departure_time": "16:30",
                "return_arrival_time": "18:30",
            }
        }
    )


class GenerateRequest(BaseModel):
    """生成旅游规划的请求模型。"""

    city: str = Field(..., description="城市名称，例如'杭州'", min_length=1)
    days: int = Field(2, description="旅游天数，默认为2天", ge=1)
    train_info: TrainInfo = Field(None, description="火车票信息，用于规划行程")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "city": "杭州",
                "days": 2,
                "train_info": {
                    "departure_train": "G105",
                    "departure_time": "08:30",
                    "arrival_time": "10:30",
                    "return_train": "G106",
                    "return_departure_time": "16:30",
                    "return_arrival_time": "18:30",
                },
            }
        }
    )


# 旧的响应模型，保留以便向后兼容
class GenerateResponse(BaseModel):
    """生成旅游规划的响应模型（旧版本）。"""

    plan: str = Field(..., description="生成的旅游规划内容")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "plan": "第一天：\n上午：参观西湖，游览苏堤春晓和白堤。\n中午：在湖边品尝杭州特色菜。\n下午：游览灵隐寺和飞来峰。\n晚上：观看印象西湖表演。\n\n第二天：\n上午：参观西溪湿地。\n中午：品尝杭帮菜。\n下午：游览宋城和体验宋城千古情表演。\n晚上：在河坊街品尝小吃，购买纪念品。"
            }
        }
    )
