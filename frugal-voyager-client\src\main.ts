import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "@/App.vue";
import router from "@/router";

// Import SCSS instead of CSS
import "@/assets/scss/main.scss";

// 导入通知服务
import NotificationPopup from "@/components/NotificationPopup/NotificationPopup.vue";
import {
  notificationState,
  closeNotification,
} from "@/services/notification-service";

const app = createApp(App);

// 注册全局组件
app.component("NotificationPopup", NotificationPopup);

// 注册全局属性
app.config.globalProperties.$notification = {
  state: notificationState,
  close: closeNotification,
};

app.use(createPinia());
app.use(router);

app.mount("#app");
