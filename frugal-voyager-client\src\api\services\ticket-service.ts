/**
 * 车票相关API服务
 */
import { api } from "../instance";
import type {
  TicketSearchRequest,
  TicketSearchResponse,
} from "../types/ticket";
import { handleErrorWithNotification } from "../utils/errorNotification";

/**
 * 车票API服务
 */
export const ticketService = {
  /**
   * 搜索车票
   * @param request - 车票搜索请求参数
   * @returns 车票搜索结果
   */
  async searchTickets(
    request: TicketSearchRequest
  ): Promise<TicketSearchResponse> {
    try {
      const response = await api.post<TicketSearchResponse>(
        "/tickets/search",
        request
      );

      // 检查是否有车票数据
      const hasOutboundTickets =
        response.data.departureTickets &&
        response.data.departureTickets.length > 0;
      const hasReturnTickets =
        response.data.returnTickets && response.data.returnTickets.length > 0;
      const hasOldFormatTickets =
        response.data.tickets && response.data.tickets.length > 0;

      // 如果需要返程票但没有找到
      if (request.return_date && !hasReturnTickets) {
        // 不显示错误通知，而是在UI中显示提示
        console.warn("未找到返程车票");
      }

      // 如果没有找到任何车票
      if (!hasOutboundTickets && !hasReturnTickets && !hasOldFormatTickets) {
        // 显示友好的错误通知
        handleErrorWithNotification(new Error("未找到车票"), {
          customMessage: `未找到从 ${request.departure_city} 到 ${request.city_name} 的车票，请尝试修改目的地或日期`,
          title: "未找到车票",
          duration: 5000, // 5秒后自动消失
        });
      }

      return response.data;
    } catch (error) {
      // 使用统一的错误处理
      handleErrorWithNotification(error, {
        customMessage: "搜索车票失败，请尝试修改目的地或日期",
        title: "搜索车票失败",
        duration: 5000, // 5秒后自动消失
      });
      // 返回空结果，避免UI出错
      return {
        departureTickets: [],
        returnTickets: [],
        // 兼容旧版本
        availableDates: [],
        tickets: [],
      };
    }
  },
};
