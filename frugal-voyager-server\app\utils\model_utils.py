"""
模型工具函数。

提供与AI模型相关的工具函数。
"""

from loguru import logger
from langchain_openai import ChatOpenAI
from app.config import settings


def get_openrouter_model(
    service_name: str = "default",
    temperature: float = 0.7,
    api_key: str = None,
    model_name: str = None,
) -> ChatOpenAI:
    """
    根据服务名称获取对应的OpenRouter模型。

    Args:
        service_name: 服务名称，用于从配置中获取对应的模型
        temperature: 模型温度参数，控制输出的随机性
        api_key: OpenRouter API Key，如果提供则使用此密钥
        model_name: 模型名称，如果提供则使用此模型

    Returns:
        配置好的ChatOpenAI模型实例
    """
    # 检查API密钥是否配置
    if api_key:
        # 使用传入的API密钥
        logger.info("使用传入的API密钥")
    elif settings.OPENROUTER_API_KEY:
        # 使用配置中的API密钥
        api_key = settings.OPENROUTER_API_KEY
    else:
        logger.warning("OpenRouter API密钥未配置，使用默认密钥")

    # 如果传入了模型名称，直接使用
    if model_name:
        logger.info(f"使用传入的模型名称: {model_name}")
    else:
        # 从映射中获取模型名称，如果不存在则使用默认模型
        model_name = settings.OPENROUTER_MODELS.get(
            service_name, settings.OPENROUTER_MODEL
        )

        # 如果模型名称为空，使用默认值
        if not model_name:
            if service_name == "default":
                model_name = "google/gemini-2.5-flash-preview"
            elif service_name == "city":
                model_name = "qwen/qwen3-235b-a22b"
            elif service_name == "travel_guide":
                model_name = "google/gemini-2.5-flash-preview"
            else:
                model_name = "google/gemini-2.5-flash-preview"

            logger.info(
                f"未配置模型，使用默认模型: {model_name} 用于服务: {service_name}"
            )
        else:
            logger.info(f"使用OpenRouter模型: {model_name} 用于服务: {service_name}")

    # 创建并返回模型实例
    return ChatOpenAI(
        api_key=api_key,
        model=model_name,
        base_url="https://openrouter.ai/api/v1",
        temperature=temperature,
        timeout=120,  # 增加超时时间
        max_retries=5,  # 增加重试次数
        default_headers={"Accept-Encoding": "identity"},
    )
