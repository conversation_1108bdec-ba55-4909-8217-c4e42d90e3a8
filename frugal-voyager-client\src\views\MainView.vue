<script setup lang="ts">
import { inject, type Ref, ref } from "vue";
import { useRouter } from "vue-router";
import DestinationSelectionView from "@/components/DestinationSelectionView/DestinationSelectionView.vue";
import TimeSelectionView from "@/components/TimeSelectionView/TimeSelectionView.vue";
import ItineraryPlanView from "@/components/ItineraryPlanView/ItineraryPlanView.vue";

// 导入可复用的功能模块
import { useStepManager } from "@/composables/useStepManager";
import { useElementAnimation } from "@/composables/useElementAnimation";
import { useDestinationAndDateSelection } from "@/composables/useDestinationAndDateSelection";
import { useComponentRefs } from "@/composables/useComponentRefs";

// 导入城市验证服务
import { validateCity } from "@/services/city-validation-service";
// 导入通知服务
import { showError, showWarning } from "@/services/notification-service";

// 获取路由实例
const router = useRouter();

// 从父组件获取引用
const mainRef = inject<Ref<HTMLElement | null>>("mainRef");
const headerRef = inject<Ref<HTMLElement | null>>("headerRef");

// 加载状态管理
const isLoading = ref(false);
const loadingText = ref("加载中...");

// 出发城市
const departureCity = ref("杭州");

// 火车票信息
const departureTrainInfo = ref<
  | { trainNumber: string; departureTime: string; arrivalTime: string }
  | undefined
>(undefined);
const returnTrainInfo = ref<
  | { trainNumber: string; departureTime: string; arrivalTime: string }
  | undefined
>(undefined);

// 显示加载状态
const showLoading = (text = "加载中...") => {
  loadingText.value = text;
  isLoading.value = true;
};

// 隐藏加载状态
const hideLoading = () => {
  isLoading.value = false;
};

// 步骤管理
const stepManager = useStepManager(0, 3);
const { currentStep, stepStates, switchStep: baseStepSwitch } = stepManager;

// 记录用户已经到达的最大步骤
const maxReachedStep = ref(0);

// 标记是否需要刷新行程计划页面
const needRefreshItineraryPlan = ref(false);

// 元素动画
const elementAnimation = useElementAnimation({ mainRef, headerRef });
const {
  // inboxVisible 虽然在模板中未直接使用，但在组件内部函数中被使用
  mainTitleRef,
  originInputContainerRef,
  inboxContainerRef,
  showInbox: baseShowInbox,
  hideInbox,
  setContentScrolling,
} = elementAnimation;

// 创建一个引用来存储DestinationSelectionView组件实例
const destinationSelectionRef = ref<InstanceType<
  typeof DestinationSelectionView
> | null>(null);

// 检查OpenRouter API Key是否已配置
const checkOpenRouterApiKey = (): boolean => {
  const openRouterKey = localStorage.getItem("openrouter_api_key");
  return !!openRouterKey && openRouterKey.trim() !== "";
};

// 重写showInbox函数，添加加载目的地数据的逻辑
const showInbox = async () => {
  // 验证出发城市是否有效
  if (!departureCity.value || departureCity.value.trim() === "") {
    // 城市名称为空，显示错误提示
    showError("请输入出发城市名称", "城市验证失败");
    return;
  }

  // 验证城市名称是否有效
  const isValid = await validateCity(departureCity.value);
  if (!isValid) {
    // 城市名称无效，显示错误提示
    showError(
      `"${departureCity.value}" 不是有效的城市名称，或者没有火车站，请输入正确的城市名称`,
      "城市验证失败"
    );
    return;
  }

  // 检查OpenRouter API Key是否已配置
  if (!checkOpenRouterApiKey()) {
    // 显示警告提示
    showWarning(
      "您尚未配置OpenRouter API Key，请先在设置页面中配置API密钥",
      "API密钥未配置"
    );

    // 跳转到设置页面
    router.push("/settings");
    return;
  }

  // 城市名称有效，显示收件箱
  baseShowInbox();

  // 重置行程计划刷新标志
  needRefreshItineraryPlan.value = false;

  // 切换到第一个步骤（目的地选择）
  switchStep(0);

  // 无论是否首次打开，都刷新数据，以便使用最新的 departureCity 值
  // 延迟一点时间，确保组件已经挂载并可见
  setTimeout(() => {
    console.log(`使用最新的出发城市: ${departureCity.value} 刷新数据`);
    refreshCurrentStep();
  }, 100);
};

// 组件引用
const componentRefs = useComponentRefs();
const {
  timeSelectionRef,
  itineraryPlanRef,
  initTimeSelectionComponent,
  initItineraryPlanComponent,
} = componentRefs;

// 目的地和日期选择
// 当选择目的地时，自动切换到时间选择步骤
// 当选择日期并确认时，自动切换到行程计划步骤
const destinationAndDateSelection = useDestinationAndDateSelection(
  (_) => {
    // 目的地选择回调：切换到时间选择页面
    switchStep(1);
  },
  (_) => {
    // 日期选择回调：切换到行程计划页面
    switchStep(2);
  }
);
const {
  // selectedDates 虽然在模板中未直接使用，但在组件内部函数中被使用
  handleDestinationSelect: baseHandleDestinationSelect,
  handleDatesSelected: baseHandleDatesSelected,
} = destinationAndDateSelection;

// 增强的目的地选择处理函数，设置需要刷新行程计划的标志
const handleDestinationSelect = (destination: string) => {
  console.log(`选择了新的目的地: ${destination}，标记需要刷新行程计划`);
  needRefreshItineraryPlan.value = true;
  baseHandleDestinationSelect(destination);
};

// 增强的日期选择处理函数，设置需要刷新行程计划的标志
const handleDatesSelected = (dates: string[]) => {
  console.log(`选择了新的日期: ${dates.join(" 至 ")}，标记需要刷新行程计划`);
  needRefreshItineraryPlan.value = true;
  baseHandleDatesSelected(dates);
};

/**
 * 刷新当前步骤的数据
 */
const refreshCurrentStep = () => {
  const index = currentStep.value;

  // 显示加载状态
  let loadingMessage = "";
  if (index === 0) loadingMessage = "正在刷新目的地选择...";
  else if (index === 1) loadingMessage = "正在刷新时间选择...";
  else if (index === 2) loadingMessage = "正在刷新行程计划...";
  showLoading(loadingMessage);

  // 根据步骤初始化相应组件
  if (index === 0) {
    // 目的地选择页面
    console.log("刷新目的地选择页面");
    console.log(`当前出发城市: ${departureCity.value}`);

    // 如果组件实例存在，调用其fetchDestinations方法，并传入当前出发城市
    if (destinationSelectionRef.value) {
      // 调用fetchDestinations方法，并传入当前出发城市
      destinationSelectionRef.value.fetchDestinations(departureCity.value);
    }
    // 确保目的地选择页面可以滚动
    setContentScrolling(true);
    console.log("目的地选择页面：启用滚动");
  } else if (index === 1) {
    // 时间选择页面
    console.log("刷新时间选择页面，初始化组件");
    const cityName = destinationAndDateSelection.selectedDestination.value;
    console.log(`当前选择的目的地城市: ${cityName || "未选择"}`);
    console.log(`当前出发城市: ${departureCity.value}`);

    // 只有当有明确的城市名称时才初始化时间选择组件
    if (cityName) {
      initTimeSelectionComponent(cityName, departureCity.value);
    } else {
      console.log("没有选择目的地城市，不初始化时间选择组件");
    }

    // 时间选择页面禁用滚动
    setContentScrolling(false);
    console.log("时间选择页面：禁用滚动");
  } else if (index === 2) {
    // 行程计划页面
    console.log("刷新行程计划页面，初始化组件");

    // 获取城市名称和日期
    let cityName = destinationAndDateSelection.selectedDestination.value;
    let selectedDates = destinationAndDateSelection.selectedDates.value;

    // 确保有城市名称和日期
    if (!cityName) {
      cityName = "上海"; // 默认城市
      console.log("没有选择城市，使用默认城市：上海");
    }

    if (!selectedDates || selectedDates.length === 0) {
      // 生成默认日期（今天和明天）
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      selectedDates = [
        today.toISOString().split("T")[0],
        tomorrow.toISOString().split("T")[0],
      ];
      console.log(`没有选择日期，使用默认日期：${selectedDates.join(" 至 ")}`);
    }

    console.log(
      `初始化行程计划组件，城市: ${cityName}, 日期: ${selectedDates.join(
        " 至 "
      )}`
    );

    // 重置火车票信息
    departureTrainInfo.value = undefined;
    returnTrainInfo.value = undefined;

    // 如果时间选择组件存在，尝试获取选中的火车票信息
    if (
      timeSelectionRef.value &&
      typeof timeSelectionRef.value.getSelectedTrains === "function"
    ) {
      const selectedTrains = timeSelectionRef.value.getSelectedTrains();

      if (selectedTrains) {
        if (selectedTrains.outbound) {
          departureTrainInfo.value = {
            trainNumber: String(selectedTrains.outbound.trainNumber || ""),
            departureTime: String(selectedTrains.outbound.departureTime || ""),
            arrivalTime: String(selectedTrains.outbound.arrivalTime || ""),
          };
          console.log(
            `获取到去程火车信息: ${departureTrainInfo.value.trainNumber}`
          );
        }

        if (selectedTrains.return) {
          returnTrainInfo.value = {
            trainNumber: String(selectedTrains.return.trainNumber || ""),
            departureTime: String(selectedTrains.return.departureTime || ""),
            arrivalTime: String(selectedTrains.return.arrivalTime || ""),
          };
          console.log(
            `获取到返程火车信息: ${returnTrainInfo.value.trainNumber}`
          );
        }
      }
    }

    // 强制重新初始化组件
    initItineraryPlanComponent(
      cityName,
      selectedDates,
      departureTrainInfo.value,
      returnTrainInfo.value
    );

    // 行程计划页面启用滚动
    setContentScrolling(true);
    console.log("行程计划页面：启用滚动");
  } else {
    // 其他页面
    console.log("刷新其他页面");
    // 默认启用滚动
    setContentScrolling(true);
    console.log("其他页面：启用滚动");
  }

  // 模拟加载延迟，然后隐藏加载状态
  setTimeout(() => {
    hideLoading();
  }, 800); // 800毫秒的加载动画，足够显示过渡效果但不会太长
};

/**
 * 增强的步骤切换函数，只处理步骤切换，不再触发数据刷新
 * @param index 步骤索引
 */
const switchStep = (index: number) => {
  // 检查是否可以跳转到该步骤（只能跳转到已经运行过的步骤或下一个步骤）
  if (index > maxReachedStep.value && index !== maxReachedStep.value + 1) {
    console.warn(
      `无法跳转到步骤 ${index}，因为当前最大步骤是 ${maxReachedStep.value}`
    );
    return;
  }

  // 显示加载状态
  let loadingMessage = "";
  if (index === 0) loadingMessage = "正在切换到目的地选择...";
  else if (index === 1) loadingMessage = "正在切换到时间选择...";
  else if (index === 2) loadingMessage = "正在切换到行程计划...";
  showLoading(loadingMessage);

  // 使用步骤管理器切换步骤
  baseStepSwitch(index);

  // 根据步骤设置滚动状态
  if (index === 0 || index === 2) {
    // 目的地选择和行程计划页面需要启用滚动
    setContentScrolling(true);
    console.log(`步骤 ${index}: 启用滚动`);
  } else if (index === 1) {
    // 时间选择页面禁用滚动
    setContentScrolling(false);
    console.log(`步骤 ${index}: 禁用滚动`);
  }

  // 如果是新的最大步骤，更新maxReachedStep并刷新数据
  if (index > maxReachedStep.value) {
    maxReachedStep.value = index;

    // 对于新步骤，需要初始化数据
    setTimeout(() => {
      refreshCurrentStep();
    }, 100);
  } else if (index === 2 && needRefreshItineraryPlan.value) {
    // 如果是切换到行程计划页面，且标记了需要刷新，则刷新数据
    console.log("检测到目的地或日期有变化，强制刷新行程计划页面");

    // 重置标志
    needRefreshItineraryPlan.value = false;

    // 刷新行程计划页面
    setTimeout(() => {
      refreshCurrentStep();
    }, 100);
  } else {
    // 对于已访问过的步骤，只切换视图，不刷新数据
    setTimeout(() => {
      hideLoading();
    }, 300);
  }
};
</script>

<template>
  <h1 class="main-title" ref="mainTitleRef">你的穷游旅行规划</h1>

  <div class="origin-input-container" ref="originInputContainerRef">
    <div class="origin-input">
      <button @click="hideInbox">↺</button>
      <input
        type="input"
        style="text-align: center"
        v-model="departureCity"
        placeholder="请输入出发城市"
        @keyup.enter="showInbox"
      />
      <button @click="showInbox">→</button>
    </div>
  </div>

  <div class="inbox-container" ref="inboxContainerRef">
    <div class="inbox-header">
      <div class="steps-container">
        <div
          :class="{
            step: true,
            active: stepStates[0].active,
            'in-progress': stepStates[0].inProgress,
            clickable: true,
          }"
          @click="switchStep(0)"
        >
          <div class="step-number">1</div>
          <div class="step-text">选择目的地</div>
        </div>
        <div class="step-divider"></div>
        <div
          :class="{
            step: true,
            active: stepStates[1].active,
            'in-progress': stepStates[1].inProgress,
            clickable: maxReachedStep >= 1,
          }"
          @click="maxReachedStep >= 1 && switchStep(1)"
        >
          <div class="step-number">2</div>
          <div class="step-text">选择时间</div>
        </div>
        <div class="step-divider"></div>
        <div
          :class="{
            step: true,
            active: stepStates[2].active,
            'in-progress': stepStates[2].inProgress,
            clickable: maxReachedStep >= 2,
          }"
          @click="maxReachedStep >= 2 && switchStep(2)"
        >
          <div class="step-number">3</div>
          <div class="step-text">计划安排</div>
        </div>
      </div>
      <div class="buttons-container">
        <button
          class="refresh-btn refresh-data-btn"
          @click="refreshCurrentStep"
        >
          刷新
        </button>
        <button class="refresh-btn close-btn" @click="hideInbox">关闭</button>
      </div>
    </div>
    <div class="inbox-content">
      <!-- 加载状态组件 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <div class="spinner-circle"></div>
          <div class="spinner-circle-inner"></div>
        </div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>

      <!-- 目的地选择页面 -->
      <DestinationSelectionView
        ref="destinationSelectionRef"
        v-show="currentStep === 0 && !isLoading"
        @select-destination="handleDestinationSelect"
        :useApi="true"
        :position="departureCity"
        :class="{
          'content-page': true,
          active: currentStep === 0 && !isLoading,
        }"
      />

      <!-- 时间选择页面 -->
      <TimeSelectionView
        v-show="currentStep === 1 && !isLoading"
        ref="timeSelectionRef"
        @dates-selected="handleDatesSelected"
        :cityName="destinationAndDateSelection.selectedDestination.value || ''"
        :departureCity="departureCity"
        :class="{
          'content-page': true,
          active: currentStep === 1 && !isLoading,
        }"
      />

      <!-- 计划安排页面 -->
      <ItineraryPlanView
        v-show="currentStep === 2 && !isLoading"
        :class="{
          'content-page': true,
          active: currentStep === 2 && !isLoading,
        }"
        ref="itineraryPlanRef"
        :cityName="destinationAndDateSelection.selectedDestination.value || ''"
        :selectedDates="destinationAndDateSelection.selectedDates.value"
        :departureTrainInfo="departureTrainInfo"
        :returnTrainInfo="returnTrainInfo"
      />
    </div>
  </div>
</template>

<style lang="scss">
@use "./MainView.scss";
</style>
