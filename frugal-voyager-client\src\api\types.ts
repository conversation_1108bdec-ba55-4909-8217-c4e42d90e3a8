/**
 * 标准API响应格式
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

/**
 * 分页元数据
 */
export interface PaginationMeta {
  currentPage: number;
  pageSize: number;
  totalPages: number;
  totalItems: number;
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T = any> extends ApiResponse {
  data: T[];
  meta: PaginationMeta;
}

/**
 * 错误响应
 */
export interface ErrorResponse {
  message: string;
  code?: string;
  details?: any;
}

/**
 * 分页请求的通用查询参数
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * 过滤请求的通用查询参数
 */
export interface FilterParams {
  search?: string;
  filters?: Record<string, any>;
  [key: string]: any;
}
