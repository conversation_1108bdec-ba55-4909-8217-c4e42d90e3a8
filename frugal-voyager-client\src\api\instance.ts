import axios from "axios";
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";
import { handleApiError } from "./utils/errorHandler";

// 默认API配置
const apiConfig: AxiosRequestConfig = {
  // 使用环境变量或默认值
  // 默认值包含API版本路径 /api/v1
  baseURL: import.meta.env.VITE_API_BASE_URL || "http://localhost:8000/api/v1",
  timeout: 300000, // 300秒超时, 5分钟
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
};

// 创建axios实例
const apiInstance: AxiosInstance = axios.create(apiConfig);

// 请求拦截器
apiInstance.interceptors.request.use(
  (config) => {
    // 添加认证令牌
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加OpenRouter API Key（如果存在）
    const openRouterKey = localStorage.getItem("openrouter_api_key");
    if (openRouterKey) {
      config.headers["X-OpenRouter-API-Key"] = openRouterKey;
    }

    // 添加OpenRouter模型配置（如果存在）
    const openRouterDefaultModel = localStorage.getItem(
      "openrouter_default_model"
    );
    const openRouterCityModel = localStorage.getItem("openrouter_city_model");
    const openRouterTravelGuideModel = localStorage.getItem(
      "openrouter_travel_guide_model"
    );

    if (openRouterDefaultModel) {
      config.headers["X-OpenRouter-Default-Model"] = openRouterDefaultModel;
    }

    if (openRouterCityModel) {
      config.headers["X-OpenRouter-City-Model"] = openRouterCityModel;
    }

    if (openRouterTravelGuideModel) {
      config.headers["X-OpenRouter-Travel-Guide-Model"] =
        openRouterTravelGuideModel;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // 如果需要，可以在这里转换响应数据
    return response;
  },
  (error: AxiosError) => {
    return Promise.reject(handleApiError(error));
  }
);

export { apiInstance };

// 常用HTTP方法的辅助函数
export const api = {
  /**
   * GET请求
   * @param url - API端点
   * @param params - 查询参数
   * @param config - 额外的axios配置
   */
  get: <T = any>(url: string, params?: any, config?: AxiosRequestConfig) => {
    return apiInstance.get<T>(url, { ...config, params });
  },

  /**
   * POST请求
   * @param url - API端点
   * @param data - 请求体
   * @param config - 额外的axios配置
   */
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return apiInstance.post<T>(url, data, config);
  },

  /**
   * PUT请求
   * @param url - API端点
   * @param data - 请求体
   * @param config - 额外的axios配置
   */
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return apiInstance.put<T>(url, data, config);
  },

  /**
   * PATCH请求
   * @param url - API端点
   * @param data - 请求体
   * @param config - 额外的axios配置
   */
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return apiInstance.patch<T>(url, data, config);
  },

  /**
   * DELETE请求
   * @param url - API端点
   * @param config - 额外的axios配置
   */
  delete: <T = any>(url: string, config?: AxiosRequestConfig) => {
    return apiInstance.delete<T>(url, config);
  },
};
