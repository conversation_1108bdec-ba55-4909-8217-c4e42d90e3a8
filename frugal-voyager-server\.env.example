# 环境设置
ENV=development

# API设置
API_V1_STR=/api/v1

# 项目信息
PROJECT_NAME=Frugal Voyager API
PROJECT_DESCRIPTION=Frugal Voyager旅行推荐应用程序的API
VERSION=0.1.0

# CORS设置
# 开发环境可以使用通配符允许所有来源
BACKEND_CORS_ORIGINS=*
# 生产环境应该指定允许的来源，例如：
# BACKEND_CORS_ORIGINS=http://localhost:5173,http://************,http://************:80,http://************:5173

# 日志配置
LOG_LEVEL=INFO

# OpenRouter API配置
# 优先级：请求头 > API临时配置 > 环境变量
# 开发环境可以在这里设置默认值，生产环境建议通过前端设置页面或API配置
OPENROUTER_API_KEY=
# 默认模型 - 用于大多数API调用
OPENROUTER_MODEL=google/gemini-2.5-flash-preview
# 城市推荐模型 - 用于城市推荐API
OPENROUTER_CITY_MODEL=qwen/qwen3-235b-a22b
# 旅游指南模型 - 用于旅游指南生成API
OPENROUTER_TRAVEL_GUIDE_MODEL=google/gemini-2.5-flash-preview

# MCP服务器配置
# 操作系统类型：windows 或 linux
MCP_OS_TYPE=windows

# Windows环境下的MCP服务器配置
MCP_WINDOWS_COMMAND=cmd
MCP_WINDOWS_ARGS=/c,npx,-y,@amap/amap-maps-mcp-server

# Linux环境下的MCP服务器配置
MCP_LINUX_COMMAND=npx
MCP_LINUX_ARGS=-y,@amap/amap-maps-mcp-server

# Tavily API配置
TAVILY_API_KEY=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
