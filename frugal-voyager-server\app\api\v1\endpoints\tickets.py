from fastapi import APIRouter
from loguru import logger

from app.schemas.ticket import TicketRequest, TicketResponse
from app.services.ticket_service import TicketService
from app.utils.exceptions import ServiceException, ResourceNotFoundException
from app.schemas.errors import ErrorCode

router = APIRouter()


@router.post("/search", response_model=TicketResponse, summary="搜索车票")
async def search_tickets(request: TicketRequest):
    """
    根据目的地城市和日期搜索可用车票。

    - **city_name**: 目的地城市名称，如'上海'、'北京'等
    - **departure_date**: 出发日期，格式为YYYY-MM-DD
    - **return_date**: 返回日期，格式为YYYY-MM-DD（可选）
    - **departure_city**: 出发城市名称，如'杭州'、'北京'等（可选，默认为'杭州'）

    返回去程和返程（如果提供了return_date）的车票信息。
    """
    try:
        logger.info(f"收到车票搜索请求: {request}")
        ticket_response = await TicketService.get_available_tickets(request)

        # 检查是否返回了有效的响应
        if not ticket_response or not ticket_response.departure_tickets:
            logger.warning("车票搜索返回了空结果")
            raise ServiceException(
                detail="未找到符合条件的车票，请尝试其他日期或目的地",
                error_code=ErrorCode.TICKET_SERVICE_ERROR,
            )

        return ticket_response
    except ServiceException:
        # 直接重新抛出已经是ServiceException的异常
        raise
    except ResourceNotFoundException:
        # 直接重新抛出ResourceNotFoundException异常
        raise
    except Exception as e:
        logger.error(f"搜索车票时出错: {e}")
        raise ServiceException(
            detail="搜索车票失败，请稍后重试", error_code=ErrorCode.TICKET_SERVICE_ERROR
        )
