"""
AI响应处理工具函数测试。
"""
import pytest
from app.utils.ai_response_utils import (
    extract_json_from_markdown,
    fix_json_format,
    ensure_complete_json,
    parse_ai_response,
)


def test_extract_json_from_markdown():
    """测试从Markdown中提取JSON。"""
    # 测试带有```json标记的情况
    markdown_with_json = """
    这是一些文本。

    ```json
    {"name": "测试", "value": 123}
    ```

    这是更多文本。
    """
    result = extract_json_from_markdown(markdown_with_json)
    assert result == '{"name": "测试", "value": 123}'

    # 测试只有```标记的情况
    markdown_with_code = """
    这是一些文本。

    ```
    {"name": "测试", "value": 123}
    ```

    这是更多文本。
    """
    result = extract_json_from_markdown(markdown_with_code)
    assert result == '{"name": "测试", "value": 123}'

    # 测试没有标记的情况
    plain_text = '{"name": "测试", "value": 123}'
    result = extract_json_from_markdown(plain_text)
    assert result == '{"name": "测试", "value": 123}'


def test_fix_json_format():
    """测试修复JSON格式。"""
    # 测试末尾逗号
    json_with_trailing_comma = '[{"name": "测试"},]'
    result = fix_json_format(json_with_trailing_comma)
    assert result == '[{"name": "测试"}]'

    # 测试单引号
    json_with_single_quotes = "{'name': 'test'}"
    result = fix_json_format(json_with_single_quotes)
    assert result == '{"name": "test"}'

    # 测试缺少引号的属性名
    json_with_unquoted_props = '{name: "test"}'
    result = fix_json_format(json_with_unquoted_props)
    assert result == '{"name": "test"}'


def test_ensure_complete_json():
    """测试确保JSON完整。"""
    # 测试完整的JSON
    complete_json = '[{"name": "test"}]'
    result = ensure_complete_json(complete_json)
    assert result == complete_json

    # 测试不完整的JSON
    incomplete_json = '[{"name": "test"}'
    result = ensure_complete_json(incomplete_json)
    assert result == incomplete_json  # 不应该修改，因为没有结束的]

    # 测试有多余内容的JSON
    json_with_extra = '[{"name": "test"}] 额外内容'
    result = ensure_complete_json(json_with_extra)
    assert result == '[{"name": "test"}]'


def test_parse_ai_response():
    """测试解析AI响应。"""
    # 测试正常的JSON响应
    valid_response = """
    ```json
    [
        {"name": "城市1", "description": "描述1"},
        {"name": "城市2", "description": "描述2"}
    ]
    ```
    """
    result = parse_ai_response(valid_response)
    assert isinstance(result, list)
    assert len(result) == 2
    assert result[0]["name"] == "城市1"
    assert result[1]["description"] == "描述2"

    # 测试格式错误的响应
    invalid_response = "这不是JSON"
    result = parse_ai_response(invalid_response)
    assert result == []
