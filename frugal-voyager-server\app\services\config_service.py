"""
配置管理服务。

提供配置的存储、获取和管理功能。
"""

from typing import Optional
from loguru import logger
from app.schemas.config import OpenRouterConfig, ConfigResponse, ConfigStatusResponse
from app.config import settings


class ConfigService:
    """配置管理服务类"""
    
    # 临时配置存储（内存中）
    _temp_openrouter_config: Optional[OpenRouterConfig] = None
    
    @classmethod
    def set_openrouter_config(cls, config: OpenRouterConfig) -> None:
        """
        设置OpenRouter临时配置
        
        Args:
            config: OpenRouter配置对象
        """
        cls._temp_openrouter_config = config
        logger.info("已设置OpenRouter临时配置")
        logger.debug(f"API Key: {config.api_key[:10]}...")
        logger.debug(f"默认模型: {config.default_model}")
        logger.debug(f"城市模型: {config.city_model}")
        logger.debug(f"旅游指南模型: {config.travel_guide_model}")
    
    @classmethod
    def get_openrouter_config(cls) -> Optional[OpenRouterConfig]:
        """
        获取OpenRouter临时配置
        
        Returns:
            OpenRouter配置对象，如果未设置则返回None
        """
        return cls._temp_openrouter_config
    
    @classmethod
    def get_effective_openrouter_config(cls) -> tuple[Optional[OpenRouterConfig], str]:
        """
        获取生效的OpenRouter配置（优先级：临时配置 > 环境变量）
        
        Returns:
            tuple: (配置对象, 配置来源)
        """
        # 优先使用临时配置
        if cls._temp_openrouter_config:
            return cls._temp_openrouter_config, "temp"
        
        # 其次使用环境变量配置
        if settings.OPENROUTER_API_KEY:
            env_config = OpenRouterConfig(
                api_key=settings.OPENROUTER_API_KEY,
                default_model=settings.OPENROUTER_MODEL or "google/gemini-2.5-flash-preview",
                city_model=settings.OPENROUTER_CITY_MODEL or "qwen/qwen3-235b-a22b",
                travel_guide_model=settings.OPENROUTER_TRAVEL_GUIDE_MODEL or "google/gemini-2.5-flash-preview"
            )
            return env_config, "env"
        
        return None, "none"
    
    @classmethod
    def clear_temp_config(cls) -> None:
        """清除临时配置"""
        cls._temp_openrouter_config = None
        logger.info("已清除OpenRouter临时配置")
    
    @classmethod
    def get_config_status(cls) -> ConfigStatusResponse:
        """
        获取配置状态
        
        Returns:
            配置状态响应
        """
        config, source = cls.get_effective_openrouter_config()
        
        if config:
            has_api_key = bool(config.api_key)
            has_models = bool(config.default_model and config.city_model and config.travel_guide_model)
            
            if has_api_key and has_models:
                message = f"配置完整，来源：{source}"
            elif has_api_key:
                message = f"有API密钥但模型配置不完整，来源：{source}"
            else:
                message = f"缺少API密钥，来源：{source}"
        else:
            has_api_key = False
            has_models = False
            message = "未配置OpenRouter"
        
        return ConfigStatusResponse(
            has_api_key=has_api_key,
            has_models=has_models,
            source=source,
            message=message
        )
    
    @classmethod
    def get_full_config(cls) -> ConfigResponse:
        """
        获取完整配置信息
        
        Returns:
            完整配置响应
        """
        config, source = cls.get_effective_openrouter_config()
        
        if config:
            return ConfigResponse(
                openrouter=config,
                is_configured=True,
                source=source
            )
        else:
            # 返回默认配置
            default_config = OpenRouterConfig(
                api_key="",
                default_model="google/gemini-2.5-flash-preview",
                city_model="qwen/qwen3-235b-a22b",
                travel_guide_model="google/gemini-2.5-flash-preview"
            )
            return ConfigResponse(
                openrouter=default_config,
                is_configured=False,
                source="none"
            )
