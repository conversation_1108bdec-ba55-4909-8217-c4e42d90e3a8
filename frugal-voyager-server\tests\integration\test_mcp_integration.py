"""
MCP集成测试。
"""

import pytest
from unittest.mock import patch, MagicMock
import json

from app.services.mcp_service import MCPService
from app.schemas.travel_guide import GuideItem


@pytest.mark.asyncio
async def test_generate_travel_plan(monkeypatch):
    """
    测试生成旅游计划。
    """
    # 模拟MCP客户端返回的JSON数据
    mock_json_data = """```json
    [
        {
            "id": "hangzhou-attraction-1",
            "type": "attraction",
            "time": "09:00 - 11:00",
            "name": "西湖",
            "description": "西湖是杭州市区西部的淡水湖，是中国大陆首批国家重点风景名胜区和中国十大风景名胜之一。",
            "ticketPrice": "免费",
            "recommendedStay": "2小时",
            "imageUrl": "https://example.com/xihu.jpg",
            "position": "left"
        },
        {
            "id": "hangzhou-transport-1",
            "type": "transportation",
            "time": "11:00 - 11:30",
            "icon": "🚌",
            "duration": "30分钟",
            "detail": "从西湖到灵隐寺"
        }
    ]
    ```"""

    # 使用monkeypatch替代patch，避免进程池序列化问题
    async def mock_generate_travel_plan(city, days):
        assert city == "杭州"
        assert days == 2
        return mock_json_data

    # 替换方法
    monkeypatch.setattr(MCPService, "generate_travel_plan", mock_generate_travel_plan)

    # 调用被测试的函数
    result = await mock_generate_travel_plan("杭州", 2)

    # 验证结果
    assert result == mock_json_data


@pytest.mark.asyncio
async def test_generate_travel_guide_items(monkeypatch):
    """
    测试生成旅游指南项目。
    """
    # 模拟生成旅游计划的返回值
    mock_json_data = """```json
    [
        {
            "id": "hangzhou-attraction-1",
            "type": "attraction",
            "time": "09:00 - 11:00",
            "name": "西湖",
            "description": "西湖是杭州市区西部的淡水湖，是中国大陆首批国家重点风景名胜区和中国十大风景名胜之一。",
            "ticketPrice": "免费",
            "recommendedStay": "2小时",
            "imageUrl": "https://example.com/xihu.jpg",
            "position": "left"
        },
        {
            "id": "hangzhou-transport-1",
            "type": "transportation",
            "time": "11:00 - 11:30",
            "icon": "🚌",
            "duration": "30分钟",
            "detail": "从西湖到灵隐寺"
        }
    ]
    ```"""

    # 使用monkeypatch替代patch，避免进程池序列化问题
    async def mock_generate_travel_plan(city, days):
        assert city == "杭州"
        assert days == 2
        return mock_json_data

    # 替换方法
    monkeypatch.setattr(MCPService, "generate_travel_plan", mock_generate_travel_plan)

    # 调用被测试的函数
    result = await MCPService.generate_travel_guide_items("杭州", 2)

    # 验证结果
    assert isinstance(result, list)
    assert len(result) == 2
    assert all(isinstance(item, GuideItem) for item in result)
    assert result[0].id == "hangzhou-attraction-1"
    assert result[0].type == "attraction"
    assert result[1].id == "hangzhou-transport-1"
    assert result[1].type == "transportation"
