# 测试指南

本目录包含Frugal Voyager API的测试文件。

## 测试结构

测试文件按照以下结构组织：

- `api/` - API端点测试
  - `v1/` - API v1端点测试
    - `test_cities.py` - 城市API测试
    - `test_tickets.py` - 车票API测试
    - `test_travel_guides.py` - 旅游指南API测试
- `integration/` - 集成测试
  - `test_mcp_integration.py` - MCP集成测试
- `utils/` - 工具函数测试
  - `test_ai_response_utils.py` - AI响应处理工具函数测试
  - `test_json_utils.py` - JSON处理工具函数测试
  - `test_string_utils.py` - 字符串处理工具函数测试

## 运行测试

### 运行所有测试

```bash
pytest
```

### 运行特定测试文件

```bash
pytest tests/api/v1/test_cities.py
```

### 运行特定测试函数

```bash
pytest tests/api/v1/test_cities.py::test_ai_recommend_cities
```

### 运行特定测试目录

```bash
pytest tests/api/
```

## 测试覆盖率

要生成测试覆盖率报告，请运行：

```bash
pytest --cov=app
```

要生成HTML格式的覆盖率报告，请运行：

```bash
pytest --cov=app --cov-report=html
```

然后在`htmlcov/index.html`中查看报告。
