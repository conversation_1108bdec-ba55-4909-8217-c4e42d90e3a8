"""
CORS中间件

用于直接在响应头中添加CORS头
"""

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from loguru import logger


class CustomCORSMiddleware(BaseHTTPMiddleware):
    """
    自定义CORS中间件，直接在响应头中添加CORS头
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        # 如果是预检请求，直接返回200 OK，不需要调用下一个处理程序
        if request.method == "OPTIONS":
            from starlette.responses import Response

            # 创建一个空的200响应
            response = Response(status_code=200)

            # 添加CORS头
            response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Access-Control-Allow-Methods"] = (
                "GET, POST, PUT, DELETE, OPTIONS, PATCH"
            )
            response.headers["Access-Control-Allow-Headers"] = "*"  # 允许所有头
            response.headers["Access-Control-Max-Age"] = (
                "86400"  # 预检请求的结果可以缓存24小时
            )

            logger.debug("已处理OPTIONS预检请求")
            return response

        # 处理非OPTIONS请求
        response = await call_next(request)

        # 添加CORS头
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = (
            "GET, POST, PUT, DELETE, OPTIONS, PATCH"
        )
        response.headers["Access-Control-Allow-Headers"] = "*"  # 允许所有头

        logger.debug("已添加自定义CORS头")
        return response
