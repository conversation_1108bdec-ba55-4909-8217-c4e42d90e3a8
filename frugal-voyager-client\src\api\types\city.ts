/**
 * 城市相关类型定义
 */

/**
 * 活动信息
 */
export interface Event {
  /** 活动名称 */
  name: string;
  /** 活动时间 */
  time: string;
  /** 活动价格 */
  price: string;
}

/**
 * 城市信息
 */
export interface City {
  /** 城市ID */
  id: string;
  /** 城市名称 */
  name: string;
  /** 城市描述 */
  description: string;
  /** 评分 */
  rating: string;
  /** 费用水平 */
  cost: string;
  /** 推荐停留时间 */
  recommendedStay: string;
  /** 城市图片URL */
  imageUrl: string;
  /** 城市活动列表 */
  events?: Event[];
}

/**
 * 城市推荐请求参数
 */
export interface CityRecommendRequest {
  /** 出发城市 */
  position: string;
}
