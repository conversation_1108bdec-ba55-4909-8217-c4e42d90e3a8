"""
配置相关的数据模型。
"""

from typing import Optional
from pydantic import BaseModel, Field


class OpenRouterConfig(BaseModel):
    """OpenRouter配置模型"""
    
    api_key: str = Field(..., description="OpenRouter API密钥")
    default_model: Optional[str] = Field(
        "google/gemini-2.5-flash-preview", 
        description="默认模型，用于大多数API调用"
    )
    city_model: Optional[str] = Field(
        "qwen/qwen3-235b-a22b", 
        description="城市推荐模型"
    )
    travel_guide_model: Optional[str] = Field(
        "google/gemini-2.5-flash-preview", 
        description="旅游指南模型"
    )


class ConfigUpdateRequest(BaseModel):
    """配置更新请求模型"""
    
    openrouter: OpenRouterConfig = Field(..., description="OpenRouter配置")


class ConfigResponse(BaseModel):
    """配置响应模型"""
    
    openrouter: OpenRouterConfig = Field(..., description="OpenRouter配置")
    is_configured: bool = Field(..., description="是否已配置")
    source: str = Field(..., description="配置来源：env（环境变量）、temp（临时配置）、header（请求头）")


class ConfigStatusResponse(BaseModel):
    """配置状态响应模型"""
    
    has_api_key: bool = Field(..., description="是否有API密钥")
    has_models: bool = Field(..., description="是否有模型配置")
    source: str = Field(..., description="配置来源")
    message: str = Field(..., description="状态描述")
