"""
JSON处理工具函数测试。
"""
import pytest
import json
from app.utils.json_utils import (
    fix_json_format,
    ensure_complete_json,
    safe_json_loads,
    format_json,
)


def test_fix_json_format():
    """测试修复JSON格式。"""
    # 测试末尾逗号
    json_with_trailing_comma = '[{"name": "测试"},]'
    result = fix_json_format(json_with_trailing_comma)
    assert result == '[{"name": "测试"}]'

    # 测试单引号
    json_with_single_quotes = "{'name': 'test'}"
    result = fix_json_format(json_with_single_quotes)
    assert result == '{"name": "test"}'

    # 测试缺少引号的属性名
    json_with_unquoted_props = '{name: "test"}'
    result = fix_json_format(json_with_unquoted_props)
    assert result == '{"name": "test"}'

    # 测试多余的双引号
    json_with_extra_quotes = '{"name"":" "test"}'
    result = fix_json_format(json_with_extra_quotes)
    assert result == '{"name": "test"}'


def test_ensure_complete_json():
    """测试确保JSON完整。"""
    # 测试完整的JSON数组
    complete_json_array = '[{"name": "test"}]'
    result = ensure_complete_json(complete_json_array)
    assert result == complete_json_array

    # 测试不完整的JSON数组
    incomplete_json_array = '[{"name": "test"}, {"name": "test2"}'
    result = ensure_complete_json(incomplete_json_array)
    assert result == incomplete_json_array  # 不应该修改，因为没有结束的]

    # 测试有多余内容的JSON数组
    json_array_with_extra = '[{"name": "test"}] 额外内容'
    result = ensure_complete_json(json_array_with_extra)
    assert result == '[{"name": "test"}]'

    # 测试完整的JSON对象
    complete_json_object = '{"name": "test"}'
    result = ensure_complete_json(complete_json_object)
    assert result == complete_json_object

    # 测试不完整的JSON对象
    incomplete_json_object = '{"name": "test", "age": 30'
    result = ensure_complete_json(incomplete_json_object)
    assert result == incomplete_json_object  # 不应该修改，因为没有结束的}

    # 测试有多余内容的JSON对象
    json_object_with_extra = '{"name": "test"} 额外内容'
    result = ensure_complete_json(json_object_with_extra)
    assert result == '{"name": "test"}'


def test_safe_json_loads():
    """测试安全地加载JSON字符串。"""
    # 测试有效的JSON
    valid_json = '{"name": "test", "age": 30}'
    result = safe_json_loads(valid_json)
    assert result == {"name": "test", "age": 30}

    # 测试无效的JSON
    invalid_json = '{"name": "test", "age": }'
    result = safe_json_loads(invalid_json)
    assert result is None

    # 测试空字符串
    empty_string = ""
    result = safe_json_loads(empty_string)
    assert result is None


def test_format_json():
    """测试格式化JSON对象为字符串。"""
    # 测试字典
    test_dict = {"name": "测试", "age": 30}
    result = format_json(test_dict)
    expected = json.dumps(test_dict, ensure_ascii=False, indent=2)
    assert result == expected

    # 测试列表
    test_list = [{"name": "测试1"}, {"name": "测试2"}]
    result = format_json(test_list)
    expected = json.dumps(test_list, ensure_ascii=False, indent=2)
    assert result == expected

    # 测试自定义缩进
    result = format_json(test_dict, indent=4)
    expected = json.dumps(test_dict, ensure_ascii=False, indent=4)
    assert result == expected
