// Shared scrollbar styles
// This file contains scrollbar styles used across multiple components

// Hide scrollbar but allow scrolling
@mixin hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

// Custom scrollbar style
@mixin custom-scrollbar {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(26, 54, 93, 0.5) rgba(255, 255, 255, 0.1); /* Firefox */
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(26, 54, 93, 0.5);
    border-radius: 4px;
    
    &:hover {
      background: rgba(26, 54, 93, 0.7);
    }
  }
}

// Apply these mixins to elements
.scrollable-hidden {
  @include hide-scrollbar;
}

.scrollable-custom {
  @include custom-scrollbar;
}
