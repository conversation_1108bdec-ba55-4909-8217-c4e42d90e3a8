import { ref } from "vue";

/**
 * 通用的项目选择逻辑
 * 用于处理列表项目的选择状态
 *
 * @param options 配置选项
 * @returns 项目选择相关的状态和方法
 */
export function useItemSelection(
  options: {
    multiple?: boolean;
    initialSelection?: any | any[];
    onSelect?: (selected: any | any[]) => void;
    onClear?: () => void;
  } = {}
) {
  const { multiple = false, initialSelection, onSelect, onClear } = options;

  // 选中的项目
  const selectedItems = ref<any[]>(
    initialSelection
      ? Array.isArray(initialSelection)
        ? [...initialSelection]
        : [initialSelection]
      : []
  );

  /**
   * 选择项目
   * @param item 要选择的项目
   * @param clearPrevious 是否清除之前的选择（多选模式下有效）
   * @returns 选择后的项目
   */
  const selectItem = (item: any, clearPrevious = true) => {
    if (multiple) {
      if (clearPrevious) {
        selectedItems.value = [item];
      } else {
        // 如果已经选中，则取消选择
        const index = selectedItems.value.findIndex((i) =>
          typeof item === "object" && item !== null
            ? JSON.stringify(i) === JSON.stringify(item)
            : i === item
        );

        if (index !== -1) {
          // 创建一个新数组，排除当前项
          selectedItems.value = [
            ...selectedItems.value.slice(0, index),
            ...selectedItems.value.slice(index + 1),
          ];
        } else {
          // 创建一个新数组，添加当前项
          selectedItems.value = [...selectedItems.value, item];
        }
      }
    } else {
      selectedItems.value = [item];
    }

    // 调用选择回调
    if (onSelect) {
      onSelect(multiple ? selectedItems.value : selectedItems.value[0]);
    }

    return multiple ? selectedItems.value : selectedItems.value[0];
  };

  /**
   * 检查项目是否被选中
   * @param item 要检查的项目
   * @returns 是否被选中
   */
  const isSelected = (item: any) => {
    return (
      selectedItems.value.findIndex((i) =>
        typeof item === "object" && item !== null
          ? JSON.stringify(i) === JSON.stringify(item)
          : i === item
      ) !== -1
    );
  };

  /**
   * 清除所有选择
   */
  const clearSelection = () => {
    selectedItems.value = [];

    // 调用清除回调
    if (onClear) {
      onClear();
    }
  };

  /**
   * 获取选中的项目
   * @returns 选中的项目
   */
  const getSelection = () => {
    return multiple ? selectedItems.value : selectedItems.value[0] || null;
  };

  return {
    selectedItems,
    selectItem,
    isSelected,
    clearSelection,
    getSelection,
  };
}
