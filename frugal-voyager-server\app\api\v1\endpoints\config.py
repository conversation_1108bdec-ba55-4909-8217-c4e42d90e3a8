"""
配置管理API端点。

提供配置的设置、获取和状态查询功能。
"""

from fastapi import APIRouter, HTTPException
from loguru import logger

from app.schemas.config import (
    ConfigUpdateRequest, 
    ConfigResponse, 
    ConfigStatusResponse,
    OpenRouterConfig
)
from app.services.config_service import ConfigService

router = APIRouter()


@router.get("/status", response_model=ConfigStatusResponse, summary="获取配置状态")
async def get_config_status():
    """
    获取当前配置状态。
    
    返回配置是否完整、配置来源等信息。
    """
    try:
        status = ConfigService.get_config_status()
        logger.info(f"配置状态查询: {status.message}")
        return status
    except Exception as e:
        logger.error(f"获取配置状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取配置状态失败")


@router.get("/", response_model=ConfigResponse, summary="获取完整配置")
async def get_config():
    """
    获取当前完整配置信息。
    
    返回所有配置项的当前值和配置来源。
    """
    try:
        config = ConfigService.get_full_config()
        logger.info(f"配置查询: 来源={config.source}, 已配置={config.is_configured}")
        return config
    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取配置失败")


@router.post("/", response_model=ConfigResponse, summary="设置配置")
async def set_config(request: ConfigUpdateRequest):
    """
    设置配置信息。
    
    此接口主要用于Swagger测试和开发调试。
    在生产环境中，建议通过前端设置页面或环境变量进行配置。
    
    - **openrouter**: OpenRouter相关配置
      - **api_key**: OpenRouter API密钥（必填）
      - **default_model**: 默认模型
      - **city_model**: 城市推荐模型
      - **travel_guide_model**: 旅游指南模型
    """
    try:
        # 验证API密钥
        if not request.openrouter.api_key.strip():
            raise HTTPException(status_code=400, detail="API密钥不能为空")
        
        # 设置配置
        ConfigService.set_openrouter_config(request.openrouter)
        
        # 返回设置后的配置
        config = ConfigService.get_full_config()
        logger.info(f"配置已更新: API Key={request.openrouter.api_key[:10]}...")
        
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail="设置配置失败")


@router.delete("/", summary="清除临时配置")
async def clear_config():
    """
    清除临时配置。
    
    清除通过API设置的临时配置，恢复使用环境变量配置。
    """
    try:
        ConfigService.clear_temp_config()
        logger.info("临时配置已清除")
        
        # 返回清除后的状态
        status = ConfigService.get_config_status()
        return {
            "message": "临时配置已清除",
            "status": status
        }
    except Exception as e:
        logger.error(f"清除配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail="清除配置失败")


@router.post("/openrouter", response_model=ConfigResponse, summary="快速设置OpenRouter配置")
async def set_openrouter_config(config: OpenRouterConfig):
    """
    快速设置OpenRouter配置。
    
    这是一个便捷接口，只需要提供OpenRouter相关配置。
    
    - **api_key**: OpenRouter API密钥（必填）
    - **default_model**: 默认模型
    - **city_model**: 城市推荐模型  
    - **travel_guide_model**: 旅游指南模型
    """
    try:
        # 验证API密钥
        if not config.api_key.strip():
            raise HTTPException(status_code=400, detail="API密钥不能为空")
        
        # 设置配置
        ConfigService.set_openrouter_config(config)
        
        # 返回设置后的配置
        full_config = ConfigService.get_full_config()
        logger.info(f"OpenRouter配置已更新: API Key={config.api_key[:10]}...")
        
        return full_config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置OpenRouter配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail="设置OpenRouter配置失败")
