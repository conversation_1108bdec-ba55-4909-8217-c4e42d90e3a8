from datetime import datetime
from typing import List, Dict, Any
from loguru import logger
import traceback

from app.schemas.city import City, CityRequest
from app.config import settings
from app.utils.ai_response_utils import (
    parse_ai_response,
    format_search_results,
)
from app.utils.exceptions import ServiceException
from app.schemas.errors import ErrorCode
from app.utils.model_utils import get_openrouter_model

# Langchain 导入
from langchain_community.utilities.tavily_search import TavilySearchAPIWrapper
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough


class CityService:
    """城市相关操作的服务。"""

    @staticmethod
    def _validate_and_transform_city_data(city_data: dict, index: int) -> dict:
        """
        验证和转换城市数据，确保所有必要的字段存在且类型正确。

        Args:
            city_data: 原始城市数据字典
            index: 城市在列表中的索引，用于日志记录

        Returns:
            验证和转换后的城市数据字典
        """
        # 创建一个新的字典，避免修改原始数据
        validated_data = {}

        # 字段映射关系
        field_mappings = {
            "title": "name",
            "price": "cost",
        }

        # 应用字段映射
        for old_field, new_field in field_mappings.items():
            if old_field in city_data and new_field not in city_data:
                city_data[new_field] = city_data.pop(old_field)
                logger.debug(f"将 '{old_field}' 映射到 '{new_field}' 字段")

        # 必要字段列表
        required_fields = [
            "id",
            "name",
            "description",
            "rating",
            "cost",
            "recommendedStay",
            "imageUrl",
        ]

        # 确保所有必要字段存在
        for field in required_fields:
            # 检查字段是否存在（考虑大小写）
            field_exists = False
            field_value = None

            # 直接匹配
            if field in city_data:
                field_exists = True
                field_value = city_data[field]
            # 尝试小写匹配
            elif field.lower() in city_data:
                field_exists = True
                field_value = city_data[field.lower()]
                logger.debug(f"使用小写字段名 '{field.lower()}' 匹配 '{field}'")

            # 如果字段不存在，设置默认值
            if not field_exists:
                logger.warning(f"城市 {index+1} 缺少必要字段: {field}")
                if field == "id":
                    field_value = f"city_{index+1}"
                elif field == "imageUrl" and "name" in city_data:
                    # 如果缺少图片URL但有城市名称，使用默认图片
                    city_name = city_data["name"]
                    logger.warning(f"城市 '{city_name}' 缺少图片URL，使用默认图片")
                    field_value = (
                        f"https://source.unsplash.com/featured/?{city_name},city,travel"
                    )
                else:
                    field_value = "未提供"

            # 确保字段值是字符串类型
            if not isinstance(field_value, str):
                original_type = type(field_value).__name__
                field_value = str(field_value)
                logger.debug(f"将 {field} 字段从 {original_type} 转换为字符串")

            # 特殊处理imageUrl字段，确保有效
            if field == "imageUrl" and (
                field_value == "未提供" or not field_value.startswith("http")
            ):
                # 如果图片URL无效，使用默认图片
                if "name" in city_data or "name" in validated_data:
                    city_name = city_data.get(
                        "name", validated_data.get("name", "city")
                    )
                    logger.warning(f"城市 '{city_name}' 的图片URL无效，使用默认图片")
                    field_value = (
                        f"https://source.unsplash.com/featured/?{city_name},city,travel"
                    )
                else:
                    field_value = "https://source.unsplash.com/featured/?city,travel"

            # 添加到验证后的数据中
            validated_data[field] = field_value

        # 处理可能的额外字段
        for field in city_data.keys():
            if field not in validated_data and field not in field_mappings:
                # 跳过已处理的字段和映射字段
                if field not in required_fields and field.lower() not in [
                    f.lower() for f in required_fields
                ]:
                    # 特殊处理 events 字段
                    if field == "events" and isinstance(city_data[field], list):
                        validated_data[field] = city_data[field]
                        logger.debug(f"添加额外字段: {field}")
                    else:
                        logger.debug(f"跳过额外字段: {field}")

        return validated_data

    @staticmethod
    def _convert_cities_data_to_objects(
        cities_data: List[Dict[str, Any]],
    ) -> List[City]:
        """
        将城市数据列表转换为 City 对象列表。

        Args:
            cities_data: 城市数据字典列表

        Returns:
            City 对象列表
        """
        cities = []
        for i, city_data in enumerate(cities_data):
            try:
                # 处理并验证城市数据
                validated_data = CityService._validate_and_transform_city_data(
                    city_data, i
                )
                if validated_data:
                    city = City(**validated_data)
                    cities.append(city)
            except Exception as city_err:
                logger.error(f"处理城市 {i+1} 数据时出错: {city_err}")
                logger.debug(f"城市数据: {city_data}")
                # 继续处理下一个城市

        return cities

    @staticmethod
    async def _validate_cities_reachable_by_train(cities: List[City]) -> List[City]:
        """
        验证城市是否可以通过火车到达（有对应的车站）。

        Args:
            cities: 城市列表

        Returns:
            可通过火车到达的城市列表
        """
        # 延迟导入，避免循环依赖
        from app.services.ticket_service import TicketService

        valid_cities = []

        for city in cities:
            # 从城市名称中提取实际城市（去除可能的景点或地区描述）
            city_name = city.name

            # 如果城市名称包含景点或地区描述，尝试提取实际城市名称
            # 例如："浙江杭州西湖" -> "杭州"
            for province in [
                "北京",
                "上海",
                "天津",
                "重庆",
                "河北",
                "山西",
                "辽宁",
                "吉林",
                "黑龙江",
                "江苏",
                "浙江",
                "安徽",
                "福建",
                "江西",
                "山东",
                "河南",
                "湖北",
                "湖南",
                "广东",
                "海南",
                "四川",
                "贵州",
                "云南",
                "陕西",
                "甘肃",
                "青海",
                "台湾",
                "内蒙古",
                "广西",
                "西藏",
                "宁夏",
                "新疆",
                "香港",
                "澳门",
            ]:
                if city_name.startswith(province) and len(city_name) > len(province):
                    # 提取省份后面的部分作为可能的城市名
                    possible_city = city_name[len(province) :]
                    # 检查是否能找到对应的车站
                    station_codes = TicketService._get_station_codes(possible_city)
                    if station_codes and station_codes[0] != "SHH":  # 不是默认的上海站
                        logger.info(f"从 '{city_name}' 提取到城市 '{possible_city}'")
                        city_name = possible_city
                        break

            # 检查是否能找到对应的车站
            station_codes = TicketService._get_station_codes(city_name)

            # 如果找不到车站，尝试使用原始城市名称
            if not station_codes or station_codes[0] == "SHH":  # 默认的上海站
                station_codes = TicketService._get_station_codes(city.name)

            # 如果能找到车站，则认为城市可达
            if station_codes and station_codes[0] != "SHH":  # 不是默认的上海站
                logger.info(
                    f"城市 '{city.name}' 可通过火车到达，车站代码: {station_codes}"
                )
                valid_cities.append(city)
            else:
                logger.warning(f"城市 '{city.name}' 无法通过火车到达或找不到对应车站")

        return valid_cities

    @staticmethod
    async def get_recommended_cities(request: CityRequest) -> List[City]:
        """
        根据用户偏好生成推荐城市。

        在实际应用中，这将调用AI服务或数据库。
        目前，我们返回模拟数据。
        """
        logger.info(f"正在为偏好生成城市推荐: {request.position}")

        # 用于演示的模拟数据
        cities = [
            City(
                id="tokyo",
                name="日本东京",
                description="东京是一座充满活力的城市，融合了传统文化与现代科技。这里有美食、购物、历史景点和先进科技，适合各类旅行者。",
                rating="4.8",
                cost="中等",
                recommendedStay="5-7 天",
                imageUrl="https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
            ),
            City(
                id="shanghai",
                name="中国上海",
                description="上海是中国最国际化的城市之一，拥有迷人的外滩、现代化的浦东新区和丰富的历史文化遗产。",
                rating="4.6",
                cost="中等",
                recommendedStay="3-5 天",
                imageUrl="https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
            ),
            City(
                id="kyoto",
                name="日本京都",
                description="京都是日本传统文化的中心，拥有众多寺庙、神社和历史遗迹，是体验日本传统文化的理想之地。",
                rating="4.9",
                cost="中高",
                recommendedStay="4-6 天",
                imageUrl="https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
            ),
        ]

        return cities

    @staticmethod
    async def get_ai_city_recommendations(request: CityRequest) -> List[City]:
        """
        使用 Langchain 结合 OpenRouter 和 Tavily 生成城市推荐。

        这是一个示例方法，展示如何使用 Langchain 调用 OpenRouter 和 Tavily。
        """
        try:
            # 检查API密钥是否配置
            if not settings.OPENROUTER_API_KEY or not settings.TAVILY_API_KEY:
                logger.warning("OpenRouter 或 Tavily API密钥未配置")
                raise ServiceException(
                    detail="AI服务未正确配置，请联系管理员",
                    error_code=ErrorCode.AI_SERVICE_ERROR,
                )

            # 获取当前时间(YYYY-MM-DD)和季节
            current_time = datetime.now()
            season = (
                "春季"
                if 3 <= current_time.month <= 5
                else (
                    "夏季"
                    if 6 <= current_time.month <= 8
                    else "秋季" if 9 <= current_time.month <= 11 else "冬季"
                )
            )
            logger.info(f"当前时间: {current_time}, 当前季节: {season}")

            current_time_str = current_time.strftime("%m月%d日")

            # 构建搜索查询
            search_query = f"{current_time_str} 适合从{request.position}出发乘坐火车前往的{season}穷游城市 花期 当地特色活动 传统节日 音乐节 必须是可以通过火车直达的城市"
            logger.info(f"使用Tavily搜索: {search_query}")

            # 初始化Tavily搜索工具
            tavily_search = TavilySearchAPIWrapper(
                tavily_api_key=settings.TAVILY_API_KEY
            )

            # 执行搜索
            search_results = tavily_search.results(
                search_query,
                search_depth="advanced",
                max_results=5,
                include_images=True,
                # include_raw_content=True,
            )

            # 格式化搜索结果
            formatted_results = format_search_results(search_results)

            # 初始化OpenRouter客户端，使用城市服务专用模型
            openrouter = get_openrouter_model(service_name="city", temperature=0.7)

            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages(
                [
                    (
                        "system",
                        """请帮我推荐一些适合从{position}乘坐火车出行的穷游城市。请严格按照输出结果输出（删除原要求之外的字段），不进行过多的讲解，只返回输出结果。具体要求如下：

                        1. **筛选标准**：
                        - 必须是可以从{position}通过火车直达的城市，不要推荐无法通过火车到达的城市
                        - 必须是具体的城市，不要推荐省份或地区
                        - 如果是推荐景点，必须明确说明景点所在的城市，并确保该城市可以通过火车到达
                        - 获取当前时间，根据当前时间进行推荐，符合当前季节或者时间出游的城市
                        - 可以结合当前季节的花期，比如春天盛开樱花的武汉，冬天冰雪覆盖的哈尔滨
                        - 可以搜索当地的是否有大型的娱乐活动或者节日，比如传统节日，音乐节，演唱会等。注意尽可能展示多种类型的活动，而不是只展示一种类型活动
                        - 当地消费水平较低，适合穷游或者 city walk
                        - 景点门票价格亲民或有免费景点
                        - 尽可能多的推荐，搜索的数量不限制，但是需要在用户可接受的等待范围内
                        - 不包含{position}，尽量选择小众人少的地方
                        - 筛选出来的活动和节日需要在活动期间，不需要提供已经过期的活动
                        - 确保每个城市都有一个有效的图片URL，如果找不到图片，可以使用该城市的知名景点图片


                        2. **信息需求**：
                        - 根据网上评价提供推荐等级
                        - 预估推荐游玩天数，格式参考： "5~10天"
                        - 预估每日基本开销（住宿+餐饮+交通）
                        - 返回的数量尽可能大于 10 个，但在 50 个以内
                        - 城市旅游平均每天预算， 格式参考："￥180/天"
                        - 活动的门票价格，格式参考：100， 0， 格式为硬性要求为字符串
                        - description 的信息尽量丰富，并说明选择的原因。


                        4. **输出格式**：
                        ```json
                        [
                            {{
                                "id": "城市拼音",
                                "name": "城市中文名称",
                                "description": "城市介绍",
                                "rating": "推荐等级，满分 5.0",
                                "cost": "城市旅游平均每天预算",
                                "recommendedStay": "推荐游玩天数",
                                "imageUrl": "网上搜索对应城市的风景图",
                                "events": [
                                    {{"name": "活动名称1", "time": "活动时间（或者时间范围，尽量不展示全年或者任何时候都有的活动，时间格式为 mm/dd ~ mm/dd）", "price": "门票价格（格式）"}},
                                    {{"name": "活动名称2", "time": "活动时间（或者时间范围，尽量不展示全年或者任何时候都有的活动，时间格式为 mm/dd ~ mm/dd）", "price": "门票价格（格式）"}}
                                ]
                            }},
                            {{
                                "id": "城市拼音2",
                                "name": "城市中文名称2",
                                "description": "城市介绍2",
                                "rating": "推荐等级，满分 5.0",
                                "cost": "城市旅游平均每天预算",
                                "recommendedStay": "推荐游玩天数",
                                "imageUrl": "网上搜索对应城市的风景图",
                                "events": [
                                    {{"name": "活动名称1", "time": "活动时间（或者时间范围，尽量不展示全年或者任何时候都有的活动，时间格式为 mm/dd ~ mm/dd）", "price": "门票价格（格式）"}},
                                    {{"name": "活动名称2", "time": "活动时间（或者时间范围，尽量不展示全年或者任何时候都有的活动，时间格式为 mm/dd ~ mm/dd）", "price": "门票价格（格式）"}}
                                ]
                            }}
                        ]
                        ```""",
                    ),
                    (
                        "human",
                        """用户偏好: {position}火车出发
                            搜索结果:
                            {search_results}""",
                    ),
                ]
            )

            # 构建Langchain链
            chain = (
                {
                    "position": lambda x: x["position"],
                    "search_results": lambda x: x["search_results"],
                }
                | prompt
                | openrouter
                | StrOutputParser()
            )

            # 执行链
            result = await chain.ainvoke(
                {
                    "position": request.position,
                    "search_results": formatted_results,
                }
            )

            # 使用辅助函数解析 AI 响应
            logger.debug(f"AI 原始响应长度: {len(result)}")
            cities_data = parse_ai_response(result)

            # 如果解析失败，抛出异常
            if not cities_data:
                logger.error("无法解析 AI 响应")
                raise ServiceException(
                    detail="无法解析AI响应，请稍后重试",
                    error_code=ErrorCode.AI_SERVICE_ERROR,
                )

            # 转换为 City 对象
            cities = CityService._convert_cities_data_to_objects(cities_data)

            # 验证城市是否可通过火车到达
            valid_cities = await CityService._validate_cities_reachable_by_train(cities)

            if valid_cities:
                logger.info(f"成功生成AI城市推荐: {len(valid_cities)}个城市")
                return valid_cities
            else:
                logger.warning("没有找到可通过火车到达的有效城市")
                raise ServiceException(
                    detail="无法找到可通过火车到达的城市，请稍后重试",
                    error_code=ErrorCode.AI_SERVICE_ERROR,
                )

        except ServiceException:
            # 直接重新抛出已经是ServiceException的异常
            raise
        except Exception as e:
            logger.error(f"调用AI服务时出错: {e}")
            logger.error(traceback.format_exc())
            raise ServiceException(
                detail="生成城市推荐时出错，请稍后重试",
                error_code=ErrorCode.AI_SERVICE_ERROR,
            )
