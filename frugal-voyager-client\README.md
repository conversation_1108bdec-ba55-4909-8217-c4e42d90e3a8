# 穷游旅行 (Frugal Voyager)

一个基于 Vue 3 + TypeScript + Vite 的旅行规划应用，帮助用户规划经济实惠的旅行行程。

## 项目简介

穷游旅行是一个专注于帮助旅行者规划经济实惠旅行的 Web 应用。通过简单直观的界面，用户可以选择目的地、规划行程时间，并获取详细的行程安排建议，包括景点、交通和住宿等信息。

## 技术栈

- **前端框架**: Vue 3
- **开发语言**: TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **样式处理**: SCSS
- **日期处理**: Day.js
- **HTTP 请求**: Axios

## 功能特点

- **目的地选择**: 浏览并选择热门旅游目的地
- **日期规划**: 选择旅行的起止日期
- **交通选择**: 查看并选择去程和返程交通方式
- **行程安排**: 获取每日详细行程规划，包括景点和交通安排
- **响应式设计**: 适配不同设备屏幕尺寸

## 项目结构

```plaintext
frugal-voyager-client/
├── public/                 # 静态资源
├── src/
│   ├── assets/             # 资源文件（图片、样式等）
│   │   └── scss/           # SCSS 样式文件
│   ├── components/         # 组件
│   │   ├── DestinationSelectionView/  # 目的地选择组件
│   │   ├── TimeSelectionView/         # 时间选择组件
│   │   └── ItineraryPlanView/         # 行程规划组件
│   ├── composables/        # 可复用的组合式函数
│   ├── router/             # 路由配置
│   ├── views/              # 页面视图
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── index.html              # HTML 模板
├── tsconfig.json           # TypeScript 配置
├── vite.config.ts          # Vite 配置
└── package.json            # 项目依赖和脚本
```

## 开发指南

### 环境要求

- Node.js 16.x 或更高版本
- pnpm 8.x 或更高版本

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产构建

```bash
pnpm preview
```

## 核心组件

### 目的地选择 (DestinationSelectionView)

用户可以浏览并选择旅行目的地，每个目的地包含名称、描述、评分、费用水平和推荐停留时间等信息。

### 时间选择 (TimeSelectionView)

用户可以选择旅行的起止日期，并查看可用的交通选项（如火车、飞机等）。

### 行程规划 (ItineraryPlanView)

根据用户选择的目的地和日期，生成详细的每日行程安排，包括景点游览和交通方式。

## 项目特色

### 组合式 API

项目大量使用 Vue 3 的组合式 API 和自定义组合函数（Composables），提高代码复用性和可维护性。

### 模块化设计

每个功能模块都被拆分为独立的组件和逻辑单元，便于维护和扩展。

### 渐进式体验

应用采用渐进式的用户体验设计，引导用户完成从目的地选择到最终行程规划的全过程。

## 未来计划

- 添加用户账户系统
- 集成实时天气信息
- 添加多语言支持
- 开发移动应用版本
- 集成地图和导航功能

## 贡献指南

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

[MIT](LICENSE)

## 联系方式

如有问题或建议，请通过 Issues 与我们联系。
