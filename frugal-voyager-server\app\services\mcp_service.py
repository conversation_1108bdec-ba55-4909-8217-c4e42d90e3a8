"""
MCP服务类，用于处理与MCP相关的操作。
"""

import asyncio
import concurrent.futures
import functools
import io
import json
import traceback
from contextlib import redirect_stdout
from typing import List, Dict, Any
from loguru import logger

from app.schemas.travel_guide import GuideItem, Transportation, Attraction
from app.utils.ai_response_utils import extract_json_from_markdown
from app.utils.exceptions import ServiceException
from app.schemas.errors import ErrorCode
from app.config import settings

# 进程池变量，将在初始化时创建
process_pool = None


class MCPService:
    """MCP相关操作的服务类。"""

    @staticmethod
    def initialize():
        """
        初始化MCP服务，创建进程池
        """
        global process_pool
        if process_pool is None:
            logger.info("初始化MCP服务进程池")
            process_pool = concurrent.futures.ProcessPoolExecutor(max_workers=2)
            logger.info("MCP服务进程池初始化完成")
        else:
            logger.info("MCP服务进程池已存在，无需重新初始化")

    @staticmethod
    def run_easy_mcp_client(
        city, days=2, train_info=None, api_key=None, model_name=None
    ):
        """
        在单独的进程中运行easy_mcp_client.py

        Args:
            city: 城市名称
            days: 旅游天数，默认为2天
            train_info: 火车票信息，包含出发和返程时间
            api_key: OpenRouter API Key，如果提供则使用此密钥
            model_name: 模型名称，如果提供则使用此模型

        Returns:
            生成的旅游规划内容
        """
        try:
            import asyncio
            from app.utils.easy_mcp_client import process_query

            # 捕获标准输出
            f = io.StringIO()
            with redirect_stdout(f):
                # 运行process_query函数，传递API Key和模型名称
                asyncio.run(process_query(city, days, train_info, api_key, model_name))

            # 返回捕获的输出
            result = f.getvalue()
            if not result:
                return f"已成功查询{city}的{days}天旅游行程，但没有返回具体内容。"
            return result
        except Exception as e:
            error_msg = f"在进程中运行easy_mcp_client时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return error_msg

    @staticmethod
    async def generate_travel_plan(city, days=2, train_info=None):
        """
        使用进程池调用easy_mcp_client.py生成旅游规划

        Args:
            city: 城市名称
            days: 旅游天数，默认为2天
            train_info: 火车票信息，包含出发和返程时间

        Returns:
            生成的旅游规划内容
        """
        global process_pool
        logger.info(f"开始为城市 {city} 生成 {days} 天的旅游规划")
        if train_info:
            logger.info(
                f"使用火车票信息: 去程={train_info.departure_train}, 返程={train_info.return_train}"
            )

        # 确保进程池已初始化
        if process_pool is None:
            MCPService.initialize()

        # 获取API Key和模型名称
        api_key = settings.OPENROUTER_API_KEY
        model_name = settings.OPENROUTER_MODELS.get(
            "travel_guide", settings.OPENROUTER_MODEL
        )

        logger.info(
            f"使用API Key: {api_key[:5]}...{api_key[-5:] if api_key else 'None'}"
        )
        logger.info(f"使用模型: {model_name}")

        try:
            # 获取当前事件循环
            loop = asyncio.get_running_loop()

            # 在进程池中运行
            logger.info("开始在进程池中执行查询")
            output = await loop.run_in_executor(
                process_pool,
                functools.partial(
                    MCPService.run_easy_mcp_client,
                    city,
                    days,
                    train_info,
                    api_key,
                    model_name,
                ),
            )

            logger.info(f"成功为城市 {city} 生成旅游规划")

            if not output:
                logger.warning(f"为城市 {city} 生成旅游规划时没有输出任何内容")
                output = f"已成功查询{city}的{days}天旅游行程，但没有返回具体内容。"

            return output
        except Exception as e:
            error_msg = f"为城市 {city} 生成旅游规划时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())

            # 如果进程池出错，尝试重新初始化
            try:
                logger.info("尝试重新初始化进程池")
                if process_pool:
                    process_pool.shutdown(wait=False)
                process_pool = concurrent.futures.ProcessPoolExecutor(max_workers=2)
                logger.info("进程池重新初始化成功")
            except Exception as e2:
                logger.error(f"重新初始化进程池时出错: {str(e2)}")

            raise ServiceException(
                detail=f"生成旅游规划时出错: {str(e) if settings.ENV == 'development' else '请稍后重试'}",
                error_code=ErrorCode.MCP_SERVICE_ERROR,
            )

    @staticmethod
    def _parse_travel_plan_to_guide_items(plan_text: str) -> List[GuideItem]:
        """
        将旅游规划文本解析为GuideItem列表

        Args:
            plan_text: 旅游规划文本，应该是JSON格式

        Returns:
            GuideItem列表
        """
        try:
            # 尝试从文本中提取JSON
            json_str = extract_json_from_markdown(plan_text)

            # 解析JSON
            items_data = json.loads(json_str)

            if not isinstance(items_data, list):
                logger.warning(f"解析的JSON不是列表格式: {type(items_data)}")
                return []

            guide_items = []

            for item_data in items_data:
                try:
                    item_type = item_data.get("type", "")

                    if item_type == "transportation":
                        # 创建Transportation对象
                        item = Transportation(
                            id=item_data.get("id", ""),
                            type="transportation",
                            time=item_data.get("time", ""),
                            icon=item_data.get("icon", "🚗"),
                            duration=item_data.get("duration", ""),
                            detail=item_data.get("detail", ""),
                        )
                        guide_items.append(item)
                    elif item_type == "attraction":
                        # 创建Attraction对象
                        item = Attraction(
                            id=item_data.get("id", ""),
                            type="attraction",
                            time=item_data.get("time", ""),
                            name=item_data.get("name", ""),
                            description=item_data.get("description", ""),
                            ticketPrice=item_data.get("ticketPrice", "免费"),
                            recommendedStay=item_data.get("recommendedStay", "1小时"),
                            imageUrl=item_data.get(
                                "imageUrl",
                                "https://images.unsplash.com/photo-1522201949034-507737bce479",
                            ),
                            position=item_data.get("position", "left"),
                        )
                        guide_items.append(item)
                    else:
                        logger.warning(f"未知的项目类型: {item_type}")
                except Exception as e:
                    logger.error(f"处理项目时出错: {str(e)}")
                    logger.error(traceback.format_exc())
                    # 继续处理下一个项目

            return guide_items
        except Exception as e:
            logger.error(f"解析旅游规划时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    @staticmethod
    async def generate_travel_guide_items(
        city: str, days: int = 2, train_info=None
    ) -> List[GuideItem]:
        """
        生成旅游规划项目列表

        Args:
            city: 城市名称
            days: 旅游天数，默认为2天
            train_info: 火车票信息，包含出发和返程时间

        Returns:
            GuideItem列表
        """
        try:
            # 获取旅游规划文本
            plan_text = await MCPService.generate_travel_plan(city, days, train_info)

            # 解析为GuideItem列表
            guide_items = MCPService._parse_travel_plan_to_guide_items(plan_text)

            if not guide_items:
                logger.warning(f"未能解析出任何旅游规划项目")
                raise ServiceException(
                    detail="未能生成旅游指南，请稍后重试",
                    error_code=ErrorCode.MCP_SERVICE_ERROR,
                )

            return guide_items
        except ServiceException:
            # 直接重新抛出已经是ServiceException的异常
            raise
        except Exception as e:
            logger.error(f"生成旅游规划项目时出错: {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(
                detail=f"生成旅游指南失败: {str(e) if settings.ENV == 'development' else '请稍后重试'}",
                error_code=ErrorCode.MCP_SERVICE_ERROR,
            )

    @staticmethod
    async def cleanup():
        """
        清理资源，关闭进程池
        """
        global process_pool
        logger.info("关闭MCP服务进程池")
        if process_pool:
            try:
                process_pool.shutdown(wait=False)
                process_pool = None
                logger.info("MCP服务进程池已关闭")
            except Exception as e:
                logger.error(f"关闭进程池时出错: {str(e)}")
                logger.error(traceback.format_exc())
