@use "variables" as *;
@use "utils/animations";
@use "utils/gradients";
@use "utils/scrollbars";
@use "components/shared-components";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

body {
  color: #333;
  min-height: 100vh;
}

#app {
  margin: 0;
  padding: 0;
  width: 100%;
}

@media (prefers-color-scheme: light) {
  :root {
    color: $text-color;
    background-color: $background-color;
  }

  button {
    background-color: #f9f9f9;
  }
}
