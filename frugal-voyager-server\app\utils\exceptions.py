"""
自定义异常类。

定义应用程序中使用的自定义异常。
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException as FastAPIHTTPException


class APIException(FastAPIHTTPException):
    """
    API异常基类，扩展FastAPI的HTTPException，添加错误代码。
    
    Args:
        status_code: HTTP状态码
        detail: 错误详情
        error_code: 错误代码
        headers: 可选的HTTP头
    """
    
    def __init__(
        self,
        status_code: int,
        detail: Any = None,
        error_code: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> None:
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code


class ResourceNotFoundException(APIException):
    """资源未找到异常。"""
    
    def __init__(
        self,
        detail: str = "请求的资源未找到",
        error_code: str = "RESOURCE_NOT_FOUND",
        headers: Optional[Dict[str, str]] = None,
    ) -> None:
        super().__init__(
            status_code=404,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class BadRequestException(APIException):
    """无效请求异常。"""
    
    def __init__(
        self,
        detail: str = "无效的请求参数",
        error_code: str = "INVALID_REQUEST",
        headers: Optional[Dict[str, str]] = None,
    ) -> None:
        super().__init__(
            status_code=400,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class ServiceException(APIException):
    """服务异常基类。"""
    
    def __init__(
        self,
        detail: str = "服务处理请求时出错",
        error_code: str = "SERVICE_ERROR",
        headers: Optional[Dict[str, str]] = None,
    ) -> None:
        super().__init__(
            status_code=500,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class ExternalServiceException(ServiceException):
    """外部服务异常。"""
    
    def __init__(
        self,
        detail: str = "调用外部服务时出错",
        error_code: str = "EXTERNAL_SERVICE_ERROR",
        headers: Optional[Dict[str, str]] = None,
    ) -> None:
        super().__init__(
            detail=detail,
            error_code=error_code,
            headers=headers,
        )
