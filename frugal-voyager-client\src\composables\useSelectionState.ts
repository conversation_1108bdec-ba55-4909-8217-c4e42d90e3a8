import { ref } from "vue";
import type { Ref } from "vue";

/**
 * Generic selection state management
 * Handles selection state for items in a list
 *
 * @param options Configuration options
 * @returns Selection state management utilities
 */
export function useSelectionState<T>(
  options: {
    multiple?: boolean;
    initialSelection?: T | T[];
    onSelect?: (selected: T | T[]) => void;
  } = {}
) {
  const { multiple = false, initialSelection, onSelect } = options;

  // Selected items
  const selectedItems = ref<T[]>(
    initialSelection
      ? Array.isArray(initialSelection)
        ? initialSelection
        : [initialSelection]
      : []
  ) as Ref<T[]>;

  /**
   * Select an item
   * @param item Item to select
   * @param clearPrevious Whether to clear previous selections (for multiple mode)
   */
  const selectItem = (item: T, clearPrevious = true) => {
    if (multiple) {
      if (clearPrevious) {
        selectedItems.value = [item];
      } else {
        // Toggle selection if already selected
        if (selectedItems.value.includes(item)) {
          selectedItems.value = selectedItems.value.filter((i) => i !== item);
        } else {
          selectedItems.value.push(item);
        }
      }
    } else {
      selectedItems.value = [item];
    }

    // Call onSelect callback if provided
    if (onSelect) {
      onSelect(multiple ? selectedItems.value : selectedItems.value[0]);
    }

    return multiple ? selectedItems.value : selectedItems.value[0];
  };

  /**
   * Check if an item is selected
   * @param item Item to check
   * @returns Whether the item is selected
   */
  const isSelected = (item: T) => {
    return selectedItems.value.includes(item);
  };

  /**
   * Clear all selections
   */
  const clearSelection = () => {
    selectedItems.value = [];

    // Call onSelect callback if provided
    if (onSelect) {
      onSelect(multiple ? [] : (undefined as unknown as T));
    }
  };

  return {
    selectedItems,
    selectItem,
    isSelected,
    clearSelection,
  };
}
