"""
错误响应模型。

定义API错误响应的标准格式。
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, ConfigDict


class ErrorDetail(BaseModel):
    """错误详情模型。"""

    loc: Optional[List[str]] = Field(None, description="错误位置，例如字段路径")
    msg: str = Field(..., description="错误消息")
    type: str = Field(..., description="错误类型")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "loc": ["body", "city"],
                "msg": "城市名称不能为空",
                "type": "value_error.missing",
            }
        }
    )


class ErrorResponse(BaseModel):
    """标准错误响应模型。"""

    status_code: int = Field(..., description="HTTP状态码")
    message: str = Field(..., description="错误消息")
    details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = Field(
        None, description="错误详情"
    )
    error_code: Optional[str] = Field(None, description="错误代码")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status_code": 400,
                "message": "请求参数无效",
                "details": [
                    {
                        "loc": ["body", "city"],
                        "msg": "城市名称不能为空",
                        "type": "value_error.missing",
                    }
                ],
                "error_code": "INVALID_REQUEST",
            }
        }
    )


# 定义常见错误代码
class ErrorCode:
    """错误代码常量。

    在生产环境中，可以考虑使用更抽象的错误代码，
    减少对内部实现的暴露。
    """

    # 通用错误 - 这些是相对安全的，可以在生产环境中使用
    INTERNAL_SERVER_ERROR = "E500"  # 服务器内部错误
    INVALID_REQUEST = "E400"  # 无效请求
    RESOURCE_NOT_FOUND = "E404"  # 资源未找到

    # 服务特定错误 - 在生产环境中可能需要更抽象
    CITY_NOT_FOUND = "E4001"  # 城市未找到
    AI_SERVICE_ERROR = "E5001"  # AI服务错误
    MCP_SERVICE_ERROR = "E5002"  # MCP服务错误
    TICKET_SERVICE_ERROR = "E5003"  # 票务服务错误
    EXTERNAL_API_ERROR = "E5004"  # 外部API错误

    # 错误代码映射 - 用于开发环境中提供更详细的错误信息
    _ERROR_DESCRIPTIONS = {
        "E500": "服务器内部错误",
        "E400": "无效请求",
        "E404": "资源未找到",
        "E4001": "城市未找到",
        "E5001": "AI服务错误",
        "E5002": "MCP服务错误",
        "E5003": "票务服务错误",
        "E5004": "外部API错误",
    }

    @classmethod
    def get_description(cls, code: str) -> str:
        """获取错误代码的描述。

        Args:
            code: 错误代码

        Returns:
            错误代码的描述，如果没有找到则返回原始代码
        """
        return cls._ERROR_DESCRIPTIONS.get(code, code)
