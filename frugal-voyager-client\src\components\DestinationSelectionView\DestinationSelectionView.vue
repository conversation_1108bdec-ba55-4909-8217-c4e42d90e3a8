<script setup lang="ts">
import { defineExpose } from "vue";
import { useDestinationSelection } from "./DestinationSelectionView";
import "./DestinationSelectionView.scss";

// 组件props和emit定义
const props = defineProps<{
  // 如果需要从父组件接收数据，在这里定义
  useApi?: boolean;
  position?: string;
}>();

const emit = defineEmits<{
  (e: "selectDestination", destination: string): void;
}>();

// 目的地选择逻辑
const {
  selectDestination: selectDestinationUtil,
  containerRef,
  destinationList,
  isLoading,
  fetchDestinations,
} = useDestinationSelection({
  onSelect: (destination) => {
    // 当选择目的地时自动触发事件
    emit("selectDestination", destination);
  },
  useApi: props.useApi ?? true, // 默认使用API
  position: props.position ?? "杭州", // 默认出发城市
});

// 选择目的地的方法
const selectDestination = (destination: string) => {
  selectDestinationUtil(destination);
};

// 暴露方法给父组件
defineExpose({
  fetchDestinations,
});
</script>

<template>
  <div class="content-page active" id="step1-content" ref="containerRef">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <div class="spinner-circle"></div>
        <div class="spinner-circle-inner"></div>
      </div>
      <div class="loading-text">正在获取推荐目的地...</div>
    </div>

    <!-- 目的地列表 -->
    <div v-else class="item-list">
      <div
        v-for="destination in destinationList"
        :key="destination.id"
        class="list-item"
        @click="selectDestination(destination.name)"
      >
        <div class="item-content">
          <div>
            <div class="item-title">{{ destination.name }}</div>
            <div class="item-description">
              {{ destination.description }}
            </div>
          </div>
          <div class="item-meta">
            <span>⭐ {{ destination.rating }}</span>
            <span>💰 {{ destination.cost }}</span>
            <span>🕒 建议停留 {{ destination.recommendedStay }}</span>
          </div>
          <!-- 显示活动信息（如果有） -->
          <div
            v-if="destination.events && destination.events.length > 0"
            class="item-events"
          >
            <div class="events-title">当地活动:</div>
            <div
              v-for="(event, index) in destination.events"
              :key="index"
              class="event-item"
            >
              <span>🎭 {{ event.name }}</span>
              <span>⏰ {{ event.time }}</span>
              <span>💲 {{ event.price }}</span>
            </div>
          </div>
        </div>
        <!-- 图片容器使用绝对定位，确保占据整个右侧区域 -->
        <div class="item-image">
          <img :src="destination.imageUrl" :alt="destination.name" />
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!isLoading && destinationList.length === 0" class="empty-state">
      <div class="empty-icon">🏙️</div>
      <div class="empty-text">暂无推荐目的地</div>
      <button class="refresh-btn" @click="() => fetchDestinations()">
        重新获取
      </button>
    </div>
  </div>
</template>
